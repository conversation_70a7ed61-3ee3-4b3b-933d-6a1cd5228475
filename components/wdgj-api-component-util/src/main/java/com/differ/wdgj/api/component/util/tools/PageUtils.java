package com.differ.wdgj.api.component.util.tools;

/**
 * 分页工具类
 *
 * <AUTHOR>
 * @date 2024/9/29 上午9:50
 */
public class PageUtils {
    //region 构造
    private PageUtils() {
    }
    //endregion

    /**
     * 计算总页数
     * @param totalCount 总记录数
     * @param pageSize 页大小
     * @return 返回总页数
     */
    public static int totalPage(int totalCount, int pageSize) {
        return totalPage((long)totalCount, pageSize);
    }

    /**
     *  计算总页数
     * @param totalCount 总记录数
     * @param pageSize 页大小
     * @return 返回总页数
     */
    public static int totalPage(long totalCount, int pageSize) {
        return pageSize == 0 ? 0 : Math.toIntExact(totalCount % (long)pageSize == 0L ? totalCount / (long)pageSize : totalCount / (long)pageSize + 1L);
    }
}
