package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.MappingSetTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncInvalidStoreResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.multi.impl.MultiBasicsDataServiceImpl;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 库存同步结果 - 无效门店
 * <AUTHOR>
 * @date 2024-03-18 18:59
 */
public class InvalidStoreStockResultProcessor extends AbstractStockResultProcessor {

    //region 构造
    /**
     * 构造
     */
    public InvalidStoreStockResultProcessor(MappingSetTypeEnum mappingSetType){
        this.mappingSetType = mappingSetType;
    }
    //endregion

    /**
     * 匹配类型
     */
    private MappingSetTypeEnum mappingSetType;

    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "无效货品";
    }

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {

        // 无效平台店铺Id列表
        List<StockSyncInvalidStoreResult> invalidStoreResults = new ArrayList<>();

        List<String> invalidStoreErrorMessage = getInvalidStoreErrorMessage();

        // 遍历失败结果,找出无效商品
        resultPackage.getFailedItems().forEach(idEnhance -> {
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (null == composite) {
                return;
            }

            // 无效平台店铺Id列表
            if (invalidStoreErrorMessage.contains(composite.getPlatResponse().getMessage())) {
                String platStoreId = composite.getPlatResponse().getPlatStoreId();
                invalidStoreResults.add(StockSyncInvalidStoreResult.create(mappingSetType, platStoreId, composite.getMatchEnhance().getSysMatch()));
            }
        });

        // 批量无效店铺删除
        if (CollectionUtils.isNotEmpty(invalidStoreResults)) {
            new MultiBasicsDataServiceImpl().delMultiStoreMatch(context.getVipUser(), context.getOperatorName(), mappingSetType, invalidStoreResults);
        }
    }

    /**
     * 获取无效平台店铺错误Message
     * @return 无效平台店铺错误Message列表
     */
    private List<String> getInvalidStoreErrorMessage(){
        String configKeyValue = ConfigKeyUtils.getWdgjConfigValue(ConfigKeyEnum.WDGJ_DeleteInvalidStoreMatch);
        return Arrays.asList(configKeyValue.split(ConfigKeyUtils.REGEX_VERTICAL_LINE));
    }
}
