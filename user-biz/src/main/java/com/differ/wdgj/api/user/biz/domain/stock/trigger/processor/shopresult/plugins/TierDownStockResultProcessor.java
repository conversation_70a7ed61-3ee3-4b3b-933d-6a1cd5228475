package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockFlagEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockRestrictedModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchExtResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.utils.StockResultUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 库存同步结果 - 降级
 *
 * <AUTHOR>
 * @date 2024-03-11 18:15
 */
public class TierDownStockResultProcessor  extends AbstractStockResultProcessor {

    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "库存同步降级";
    }

    /**
     * 处理结果
     *
     * @param context       上下文
     * @param resultPackage 库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        // 获取降级时间
        Map<String, Integer> gradeTimeConfig = StockResultUtils.getTierDownGradeTimeConfig();

        // 需要降级的菠萝派错误（后续可以依赖配置键）
        List<String> needTierDownErrorCodes = getNeedTierDownErrorCodes();

        // 遍历失败结果
        resultPackage.getFailedItems().forEach(idEnhance -> {
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (null == composite) {
                return;
            }

            String errorCodeName = composite.getResponseErrorCode().name();
            boolean isNeedProcess = resultPackage.getNeedProcessErrorCodes().contains(composite.getResponseErrorCode().name());
            if(isNeedProcess && needTierDownErrorCodes.contains(errorCodeName)){
                // 降级时间
                Integer gradeTime = gradeTimeConfig.getOrDefault(errorCodeName, 0);
                LocalDateTime nextResetSyncTime = LocalDateTime.now().plusSeconds(gradeTime);
                // 降级标识
                SyncStockFlagEnum limit = SyncStockFlagEnum.Limit;
                // 限制/禁止模式
                SyncStockRestrictedModeEnum restrictedMode = composite.getResponseErrorCode().getRestrictedMode();

                // 结果持久化
                StockSyncApiSysMatchExtResult tierDown = composite.getApiSysMatchExtResult() != null
                        ? composite.getApiSysMatchExtResult()
                        : StockSyncApiSysMatchExtResult.createDefault(idEnhance.getMatchId());
                tierDown.setIncreFlag(limit.getValue());
                tierDown.setNextResetSyncTime(nextResetSyncTime);
                tierDown.setRestrictedMode(restrictedMode.getValue());
                composite.setApiSysMatchExtResult(tierDown);
            }
        });
    }

    /**
     * 获取 需要降级的菠萝派错误
     * @return 需要降级的菠萝派错误
     */
    private List<String> getNeedTierDownErrorCodes(){
        List<String> needTierDownErrorCodes = new ArrayList<>();
        needTierDownErrorCodes.add(SyncStockPolyErrorCodeEnum.UP_OPERATE_FAIL.name());
        needTierDownErrorCodes.add(SyncStockPolyErrorCodeEnum.FIRERULES.name());
        needTierDownErrorCodes.add(SyncStockPolyErrorCodeEnum.FREQUENTOPERATION.name());
        needTierDownErrorCodes.add(SyncStockPolyErrorCodeEnum.BEYONDFREQUENCYLIMIT.name());
        needTierDownErrorCodes.add(SyncStockPolyErrorCodeEnum.SERVICE_UNAVAILABLE.name());
        needTierDownErrorCodes.add(SyncStockPolyErrorCodeEnum.STOCK_PLATOPERATIONLIMIT.name());
        needTierDownErrorCodes.add(SyncStockPolyErrorCodeEnum.STOCK_PLATSERVICEERROR.name());
        return needTierDownErrorCodes;
    }
}
