package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop.ApiShopCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevShopDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopLocalKey;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.OuterApiEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopAuthStatusEnum;
import org.apache.commons.lang3.RandomUtils;

import java.util.concurrent.TimeUnit;

/**
 * 店铺基础数据内存缓存
 *
 * <AUTHOR>
 * @date 2024-04-03 16:53
 */
public class ApiShopBaseLocalCache extends AbstractLocalCache<ApiShopLocalKey, ApiShopBaseDto> {

    //region 构造单例

    private ApiShopBaseLocalCache() {
        expire = RandomUtils.nextInt(300, 600);
        timeUnit = TimeUnit.SECONDS;
        // 最大数据个数
        cacheMaxSize = 300000;
    }

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static ApiShopBaseLocalCache singleton() {
        return ApiShopBaseLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final ApiShopBaseLocalCache instance;

        private SingletonEnum() {
            instance = new ApiShopBaseLocalCache();
        }
    }

    //endregion

    // region 公共方法

    /**
     * 获取店铺配置
     *
     * @param outAccount 外部会员名
     * @param shopId     店铺id
     * @return 店铺配置
     */
    public ApiShopBaseDto getInfo(String outAccount, int shopId) {

        // 校验入参
        if (shopId == 0 || outAccount == null) {
            return null;
        }

        // 查询缓存
        return this.getCacheThenSource(ApiShopLocalKey.create(outAccount, shopId));
    }

    // endregion

    //region 重写基类方法

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key 键
     * @return 值
     */
    @Override
    protected ApiShopBaseDto loadSource(ApiShopLocalKey key) {
        if (key != null) {
            DevShopDO shopDO = ApiShopCache.create(key.getOutAccount()).get(key.getShopId());
            if (shopDO != null) {
                PolyPlatEnum poly = PolyPlatEnum.create(shopDO.getPlatValue());
                OuterApiEnum outerApi = OuterApiEnum.create(shopDO.getOutPlatTypes());
                ShopAuthStatusEnum shopAuthStatus = ShopAuthStatusEnum.create(shopDO.getAuthStatus());

                // 创建内存缓存对象
                ApiShopBaseDto apiShopDto = new ApiShopBaseDto();
                apiShopDto.setShopId(shopDO.getShopId());
                apiShopDto.setOutShopId(shopDO.getOutShopId());
                apiShopDto.setOutAccount(shopDO.getOutAccount());
                apiShopDto.setOutPlatTypes(outerApi);
                apiShopDto.setShopName(shopDO.getShopName());
                apiShopDto.setToken(shopDO.getToken());
                apiShopDto.setPlat(poly);
                apiShopDto.setAuthStatus(shopAuthStatus);
                apiShopDto.setActived(shopDO.isActived());
                apiShopDto.setDelete(shopDO.isDelete());
                apiShopDto.setPlatShopId(shopDO.getPlatShopId());
                apiShopDto.setPlatShopName(shopDO.getPlatShopName());
                return apiShopDto;
            }
        }
        return null;
    }

    //endregion
}
