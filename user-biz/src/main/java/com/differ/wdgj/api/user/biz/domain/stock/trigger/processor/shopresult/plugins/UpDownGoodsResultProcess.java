package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.goods.match.data.ApiSysMatchOperationEnhance;
import com.differ.wdgj.api.user.biz.domain.goods.match.subdomain.impl.BasicsGoodsMatchServiceImpl;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.GoodsShelfStateEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.exmsg.ExMsgNoticeService;
import com.differ.wdgj.api.user.biz.domain.wdgj.exmsg.data.SyncStockFailNoticeInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 库存同步结果 - 上下架失败
 *
 * <AUTHOR>
 * @date 2024-03-12 20:40
 */
public class UpDownGoodsResultProcess extends AbstractStockResultProcessor {
    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "上下架";
    }

    /**
     * 处理结果
     *
     * @param context       上下文
     * @param resultPackage 库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        // 上下架成功处理
        upDownSuccessProcess(context, resultPackage);
        // 上下架失败处理
        upDownFailedProcess(context, resultPackage);
    }

    //region 私有方法

    /**
     * 上下架成功处理
     * @param context       上下文
     * @param resultPackage 库存同步结果包
     */
    private void upDownSuccessProcess(StockSyncContext context, StockSyncResultPackage resultPackage){
        // 上架成功货品
        Set<ApiSysMatchOperationEnhance> upGoodsItems = new HashSet<>();

        // 获取上架成功的货品
        resultPackage.getComposites().forEach((idEnhance, result) -> {
            // 库存同步成功的上架请求，且当前状态是未上架的
            if(Boolean.TRUE.equals(result.getPlatResponse().getSuccess()) &&
                    GoodsShelfStateEnum.OnSale.getCode().equalsIgnoreCase(result.getPlatRequest().getStatus()) &&
                    !GoodsShelfStateEnum.OnSale.getValue().equals(result.getMatchEnhance().getSysMatch().getShelfState())){
                String operationReason = "请求平台接口上架成功";
                upGoodsItems.add(ApiSysMatchOperationEnhance.create(result.getMatchEnhance().getSysMatch(), operationReason));
            }
        });

        // 上架商品更新
        if(CollectionUtils.isNotEmpty(upGoodsItems)){
            new BasicsGoodsMatchServiceImpl().updateGoodsMatchShelfState(context.getVipUser(), context.getOperatorName(), GoodsShelfStateEnum.OnSale, new ArrayList<>(upGoodsItems));
        }

    }

    /**
     * 上下架失败处理
     * @param context       上下文
     * @param resultPackage 库存同步结果包
     */
    private void upDownFailedProcess(StockSyncContext context, StockSyncResultPackage resultPackage){
        // 是否支持会员级上下架失败通知
        if(!ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.IsAction_SyncStockFrequecnyControl_ShelvesFailedNotify, context.getVipUser())){
            return;
        }

        // 构建通知数据
        List<SyncStockFailNoticeInfo> failNoticeInfoList = new ArrayList<>();
        resultPackage.getFailedItems().forEach(idEnhance ->{
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (null == composite) {
                return;
            }

            boolean isUpDownFailErrorCode = SyncStockPolyErrorCodeEnum.FIRERULES.equals(composite.getResponseErrorCode()) ||
                    SyncStockPolyErrorCodeEnum.UP_OPERATE_FAIL.equals(composite.getResponseErrorCode());
            if(isUpDownFailErrorCode){
                SyncStockFailNoticeInfo failNoticeInfo = new SyncStockFailNoticeInfo();
                failNoticeInfo.setAuto(StockSyncTriggerTypeEnum.AUTO_SYNC.equals(context.getTriggerType()));
                failNoticeInfo.setApiSysMatchId(idEnhance.getMatchId());
                failNoticeInfo.setSyncMsg(composite.getPlatResponse().getMessage());
                failNoticeInfo.setShopId(context.getShopId());
                failNoticeInfo.setShopName(context.getShopBase().getShopName());
                failNoticeInfo.setTbName(composite.getMatchEnhance().getSysMatch().gettBName());
                failNoticeInfo.setModeEnum(composite.getResponseErrorCode().getRestrictedMode());
                failNoticeInfoList.add(failNoticeInfo);
            }
        });

        // 通知库存同步失败
        if(CollectionUtils.isNotEmpty(failNoticeInfoList)){
            new ExMsgNoticeService().noticeSyncStockFail(context.getVipUser(), failNoticeInfoList);
        }
    }

    //endregion
}
