package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.goods.match.data.ApiSysMatchOperationEnhance;
import com.differ.wdgj.api.user.biz.domain.goods.match.subdomain.impl.BasicsGoodsMatchServiceImpl;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/**
 * 库存同步结果 - 停用库存同步
 * <AUTHOR>
 * @date 2024-03-11 17:37
 */
public class DisableStockResultProcessor extends AbstractStockResultProcessor {
    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "停用库存同步";
    }

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {

        // 无效商品
        Set<ApiSysMatchOperationEnhance> invalidItems = new HashSet<>();

        // 遍历失败结果,找出无效商品
        resultPackage.getFailedItems().forEach(idEnhance -> {
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (null == composite) {
                return;
            }

            // 菠萝派异常停用库存同步
            boolean isNeedProcess = resultPackage.getNeedProcessErrorCodes().contains(composite.getResponseErrorCode().name());
            if (isNeedProcess && SyncStockPolyErrorCodeEnum.STOCK_DISABLE.equals(composite.getResponseErrorCode())) {
                invalidItems.add(ApiSysMatchOperationEnhance.create(composite.getMatchEnhance().getSysMatch(), composite.getPlatResponse().getMessage()));
            }
        });

        // 停用商品匹配
        if (CollectionUtils.isNotEmpty(invalidItems)) {
            new BasicsGoodsMatchServiceImpl().blockUpGoodsMatch(context.getVipUser(), context.getOperatorName(), new ArrayList<>(invalidItems));
        }
    }

}
