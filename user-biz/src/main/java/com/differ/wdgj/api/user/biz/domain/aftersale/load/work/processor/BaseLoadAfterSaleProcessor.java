package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.component.util.tools.PageUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.*;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleLogUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.create.CreateSubTaskOperatorFactory;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.create.ICreateSubTask;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.load.ILoadSubTask;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.load.LoadSubTaskOperatorFactory;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.save.SaveAfterSaleOperator;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.LoadAfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.AfterSaleQueryTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.RunPageResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.TaskSplitStrategyContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.mode.run.RunMode;
import com.differ.wdgj.api.user.biz.infrastructure.work.mode.run.page.AscPageRunMode;
import com.differ.wdgj.api.user.biz.infrastructure.work.mode.run.page.DescPageRunMode;
import com.differ.wdgj.api.user.biz.infrastructure.work.mode.run.page.NextTokenPageRunMode;
import com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy.DynamicTaskSplitStrategy;
import com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy.FixTaskSplitStrategy;
import com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy.NoTaskSplitStrategy;
import com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy.TaskSplitStrategy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 售后单下载工作任务的业务处理基础逻辑
 *
 * <AUTHOR>
 * @date 2024/9/11 下午2:26
 */
public class BaseLoadAfterSaleProcessor extends AbstractLoadAfterSaleProcessor {
    //region 常量
    /**
     * 标题
     */
    protected final String caption = "售后单下载";

    /**
     * 日志对象
     */
    protected final Logger log = LogFactory.get(caption);
    //endregion

    //region 构造
    public BaseLoadAfterSaleProcessor(AfterSaleLoadTaskContext context) {
        super(context);
    }
    //endregion

    // region 实现基类方法

    /**
     * 创建子任务
     *
     * @param workData 工作任务数据
     * @return 返回子任务集合
     */
    @Override
    public final List<AfterSaleLoadSubTask> createSubTask(WorkData<AfterSaleLoadArgs> workData) {
        // 基础子任务数据构建
        ICreateSubTask operator = CreateSubTaskOperatorFactory.build(workData.getData().getLoadType(), context, this);
        List<AfterSaleLoadSubTask> subTasks = operator.create(workData);
        if (CollectionUtils.isEmpty(subTasks)) {
            return Collections.emptyList();
        }

        // 平台级特殊处理
        subTasks = batchSubTaskCreatePlatProcess(workData, subTasks);

        // 调试日志
        List<AfterSaleLoadSubTask> finalSubTasks = subTasks;
        LoadAfterSaleLogUtils.loadInfoLog(context, "售后单下载创建子任务", () -> ExtUtils.stringBuilderAppend("创建子任务:", JsonUtils.toJson(finalSubTasks)));

        return subTasks;
    }

    /**
     * 业务处理
     *
     * @param subTask 子任务（原则上业务内不修改子任务的值）
     * @return 运行结果
     */
    @Override
    public final RunPageResult doLoad(final AfterSaleLoadSubTask subTask) {
        // 初始化结果
        RunPageResult pageResult = RunPageResult.running();

        try {
            // 获取菠萝派下载接口兼容逻辑，菠萝派后续会将所有售后单下载集成到接口"Differ.JH.Business.GetRefund"
            AfterSaleQueryTypeEnum interfaceQueryType = AfterSaleQueryTypeEnum.create(subTask.getInterfaceQueryType());
            if (interfaceQueryType == null) {
                interfaceQueryType = AfterSaleQueryTypeEnum.POLY_GET_REFUND;
            }

            // 请求之前特殊处理
            onBeforeRequest(interfaceQueryType, subTask);

            // 发起下载售后单
            ILoadSubTask loadOperator = LoadSubTaskOperatorFactory.build(interfaceQueryType, context);
            LoadAfterSaleSubTaskResult loadResult = loadOperator.load(subTask);
            if (loadResult == null) {
                String errorMessage = "下载售后单返回空";
                return pageResult.onFail(LoadAfterSaleWorkResult.onFail(subTask, errorMessage), errorMessage);
            }
            if (loadResult.isFailed()) {
                String errorMessage = StringUtils.isNotEmpty(loadResult.getPolyApiRequestId())
                        ? String.format("[%s]请求平台异常：%s；%s", loadResult.getPolyApiRequestId(), loadResult.getMessage(), loadResult.getSubMessage())
                        : String.format("下载售后单失败：%s", loadResult.getMessage());
                return pageResult.onFail(LoadAfterSaleWorkResult.onFail(subTask, errorMessage), errorMessage);
            }

            // 保存售后单
            SaveAfterSaleOperator saveOperator = new SaveAfterSaleOperator(context);
            SaveLoadedAfterSaleResult saveResult = saveOperator.save(loadResult.getLoadOrders());
            if (saveResult == null || saveResult.isFailed()) {
                String saveMessage = saveResult == null ? "保存售后单返回空" : saveResult.getMessage();
                String errorMessage = String.format("【下载售后单后保存全部失败】%s", saveMessage);
                return pageResult.onFail(LoadAfterSaleWorkResult.onFail(subTask, errorMessage), errorMessage);
            }

            // 结果整合
            combinationLoadResult(subTask, loadResult, pageResult);

            // 请求之后特殊处理
            onAfterRequest(interfaceQueryType, subTask, loadResult, pageResult);
        } finally {
            // 调试日志
            LoadAfterSaleLogUtils.loadInfoLog(context, "售后单下载保存", () -> ExtUtils.stringBuilderAppend(String.format("下载售后单，子任务信息：%s；结果：%s。", JsonUtils.toJson(subTask), JsonUtils.toJson(pageResult))));
        }

        return pageResult;
    }

    /**
     * 合并结果
     *
     * @param taskResultDataList 任务结果数据集合
     * @return 合并结果
     */
    @Override
    public final LoadAfterSaleWorkResult merge(WorkResult... taskResultDataList) {
        if (null == taskResultDataList || taskResultDataList.length == 0) {
            return null;
        }
        LoadAfterSaleWorkResult workResult = LoadAfterSaleWorkResult.onSuccess();
        for (WorkResult taskResult : taskResultDataList) {
            LoadAfterSaleWorkResult subLoadResult = (LoadAfterSaleWorkResult) taskResult;
            // 一个失败视为整体失败
            if (!subLoadResult.success()) {
                workResult.setMessage(subLoadResult.getMessage());
                workResult.setSuccess(subLoadResult.success());
            }
            if (subLoadResult.getDetails() != null) {
                workResult.mergeResultDetails(subLoadResult.getDetails().toArray(new LoadAfterSaleDetail[0]));
            }
        }

        return workResult;
    }

    /**
     * 创建运行模式
     *
     * @param taskId   任务id
     * @param workData 工作任务数据
     * @param subTask  子任务
     * @return 任务允许模式
     */
    @Override
    public final RunMode createRunMode(String taskId, WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
        RunMode loadModel = null;
        // 根据业务特性设置下载模式
        LoadAfterSalesConfigContent platFeature = context.getDownloadAfterSalesPlatFeature(subTask.getApiShopType());
        if (platFeature != null) {
            switch (platFeature.getLoadOrderPageType()) {
                case DESC:
                    loadModel = new DescPageRunMode(taskId, workData);
                    break;
                case NEXT:
                    loadModel = new NextTokenPageRunMode(taskId, workData);
                    break;
                case ASC:
                default:
                    loadModel = new AscPageRunMode(taskId, workData);
                    break;
            }
        }

        // 按单号下载仅支持正序模式
        AfterSaleLoadArgs loadArgs = workData.getData();
        if (loadArgs.getLoadType() == AfterSaleLoadTypeEnum.AFTER_SALE_NO) {
            loadModel = new AscPageRunMode(taskId, workData);
        }
        return loadModel;
    }

    /**
     * 创建拆分策略
     *
     * @param taskId   任务id
     * @param workData 工作任务数据
     * @param subTask  子任务
     * @return 拆分策略
     */
    @Override
    public final TaskSplitStrategy createSplitStrategy(String taskId, WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
        TaskSplitStrategy splitStrategy = null;
        LoadAfterSalesConfigContent platFeature = context.getDownloadAfterSalesPlatFeature(subTask.getApiShopType());

        // 拆分策略上下文对象
        TaskSplitStrategyContext splitStrategyContext = new TaskSplitStrategyContext();
        if (platFeature != null) {
            splitStrategyContext.setMaxQueryRange(platFeature.getPlatMaxQueryRangeLimit());
            splitStrategyContext.setOffset(platFeature.getIncrementMaxRangeLimit());

            // 根据业务特性设置拆分策略
            switch (platFeature.getLoadSplitType()) {
                case FIX_TIME_SPLIT:
                    splitStrategy = new FixTaskSplitStrategy(splitStrategyContext);
                    break;
                case DYNAMIC_TIME_SPLIT:
                    splitStrategy = new DynamicTaskSplitStrategy(splitStrategyContext);
                    break;
                case NO_SPLIT:
                default:
                    splitStrategy = new NoTaskSplitStrategy(splitStrategyContext);
                    break;
            }
        }

        return splitStrategy;
    }

    // endregion

    //region 供平台重写方法
    //region 子任务创建

    /**
     * 子任务业务列表平台级特殊处理
     * <p>通常用于任务列表的扩展，例如速卖通Miravia子店铺业务
     *
     * @param workData        工作任务数据
     * @param shopRefundTypes 店铺类型-售后类型列表
     * @return 特殊处理后子任务列表
     */
    public List<AfterSaleBizType> subTaskBizListPlatProcess(WorkData<AfterSaleLoadArgs> workData, List<AfterSaleBizType> shopRefundTypes) {
        return shopRefundTypes;
    }

    /**
     * 批量子任务平台级特殊处理
     *
     * @param workData 工作任务数据
     * @param subTasks 子任务列表
     * @return 特殊处理后子任务列表
     */
    protected final List<AfterSaleLoadSubTask> batchSubTaskCreatePlatProcess(WorkData<AfterSaleLoadArgs> workData, List<AfterSaleLoadSubTask> subTasks) {
        List<AfterSaleLoadSubTask> targetSubTasks = new ArrayList<>();
        subTasks.forEach(subTask -> {
            LoadAfterSaleWorkResult result = subTaskCreatePlatProcess(workData, subTask);
            if (result.failed()) {
                // todo 记录调试日志
                return;
            }

            targetSubTasks.add(subTask);
        });

        return targetSubTasks;
    }

    /**
     * 子任务平台级特殊处理
     *
     * @param workData 工作任务数据
     * @param subTask  子任务
     * @return 特殊处理后子任务
     */
    protected LoadAfterSaleWorkResult subTaskCreatePlatProcess(WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
        return LoadAfterSaleWorkResult.onSuccess();
    }
    //endregion

    //region 下载售后单

    /**
     * 请求之前特殊处理
     *
     * @param loadType 业务类型
     * @param subTask  子任务
     */
    protected void onBeforeRequest(AfterSaleQueryTypeEnum loadType, AfterSaleLoadSubTask subTask) {
        // 供子类重写
    }

    /**
     * 请求之后特殊处理
     *
     * @param loadType      业务类型
     * @param subTask       子任务
     * @param runPageResult 当前执行任务结果
     * @param loadResult    下载结果
     */
    protected void onAfterRequest(AfterSaleQueryTypeEnum loadType, AfterSaleLoadSubTask subTask, LoadAfterSaleSubTaskResult loadResult, RunPageResult runPageResult) {
        // 供子类重写
    }
    //endregion
    //endregion

    //region 私有方法

    /**
     * 组合 下载/保存结果
     *
     * @param subTask    子任务
     * @param loadResult 下载结果
     * @param pageResult 结果
     */
    private void combinationLoadResult(AfterSaleLoadSubTask subTask, LoadAfterSaleSubTaskResult loadResult, RunPageResult pageResult) {
        // 基础参数
        AfterSalePageArgs pageArgs = loadResult.getPageArgs();
        // 分页相关基础参数
        pageResult.setNextToken(pageArgs.getNextToken());
        pageResult.setHasNextPage(pageArgs.isHasNextPage());
        pageResult.setDataSize(pageArgs.getDataSize());
        pageResult.setLastSuccessDataTime(pageArgs.getLastSuccessDataTime());
        // 第一页赋值
        if (subTask.getDynamicStatus().getPageIndex() == 1) {
            int nowTotalPage = PageUtils.totalPage(pageArgs.getTotalCount(), subTask.getPageSize());
            LoadAfterSalesConfigContent platFeature = context.getDownloadAfterSalesPlatFeature(subTask.getApiShopType());
            int maxTotalPage = PageUtils.totalPage(platFeature.getPlatMaxNumLimit(), subTask.getPageSize());
            int totalPage = Math.min(nowTotalPage, maxTotalPage);
            // 总页数
            pageResult.setTotalPages(totalPage);
            // 平台级页数限制
            pageResult.setMaxTotalPages(maxTotalPage);
        }

        // 工作任务数据
        pageResult.setWorkResult(LoadAfterSaleWorkResult.onSuccess(subTask, loadResult));
    }
    //endregion
}
