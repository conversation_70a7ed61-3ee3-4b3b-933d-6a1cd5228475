package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.goods.match.subdomain.impl.BasicsGoodsMatchServiceImpl;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.GoodsPolyEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/**
 * 库存同步结果 - 淘宝分销商品
 * <AUTHOR>
 * @date 2024-03-20 14:31
 */
public class TaoBaoFxStockResultProcessor extends AbstractStockResultProcessor {
    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "淘宝分销商品";
    }

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        Set<Integer> needUpdateFxMatchIds = new HashSet<>();

        // 遍历失败结果
        resultPackage.getFailedItems().forEach(idEnhance -> {
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (composite == null) {
                return;
            }

            // 分销商品异常
            if(SyncStockPolyErrorCodeEnum.FEN_XIAO.equals(composite.getResponseErrorCode())){
                needUpdateFxMatchIds.add(idEnhance.getMatchId());
            }
        });

        // 批量更新淘宝分销商品
        if(CollectionUtils.isNotEmpty(needUpdateFxMatchIds)){
            new BasicsGoodsMatchServiceImpl().updateGoodsBTbGoods(context.getVipUser(), GoodsPolyEnum.APISys_TBFX, new ArrayList<>(needUpdateFxMatchIds));
        }
    }
}
