package com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.*;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.AfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.LoadAfterSalesConfigContent;

/**
 * 平台业务特性类型枚举
 *
 * <AUTHOR>
 * @date 2024-06-20 18:16
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum PlatBizFeatureTypeEnum implements ValueEnum, NameEnum {

    /**
     * 下载售后单
     */
    LOAD_AFTER_SALES(1, "下载售后单", LoadAfterSalesConfigContent.class),

    /**
     * 售后单配置
     */
    AFTER_SALES_CONFIG(2, "售后单配置", AfterSalesConfigContent.class),

    ;

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 名称
     */
    private final String name;

    /**
     * 配置内容
     */
    private final Class<?> configContentClazz;

    /**
     * 构造方法
     *
     * @param type 类型
     * @param name 名称
     */
    PlatBizFeatureTypeEnum(Integer type, String name, Class<?> configContentClazz) {
        this.type = type;
        this.name = name;
        this.configContentClazz = configContentClazz;
    }

    /**
     * 获取类型
     *
     * @return 类型
     */
    @Override
    public Integer getValue() {
        return this.type;
    }

    /**
     * 获取名称
     *
     * @return 名称
     */
    @Override
    public String getName() {
        return this.name;
    }

    /**
     * 获取配置内容类型
     *
     * @return 类型
     */
    public Class<?> getConfigContentClazz() {
        return configContentClazz;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static PlatBizFeatureTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, PlatBizFeatureTypeEnum.class, EnumConvertType.VALUE);
    }
}
