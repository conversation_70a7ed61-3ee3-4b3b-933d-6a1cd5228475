package com.differ.wdgj.api.user.biz.domain.stock.data.mq;

import com.differ.wdgj.api.user.biz.domain.stock.data.SyncStockJsonBackData;

import java.time.LocalDateTime;
import java.util.List;

/**
 * dotNet库存同步结果
 * todo：后续类迁移到mq相关包
 *
 * <AUTHOR>
 * @date 2024-03-01 14:08
 */
public class DotNetSyncStockResult {

    /**
     * API用户名
     */
    private String apiUserName;

    /**
     * 外部会员名
     */
    private String outAccount;

    /**
     * API登录名
     */

    private String loginName;

    /**
     * 请求id
     */
    private String contextId;

    /**
     * 同步结果列表
     */
    private List<SyncResult> syncResultData;

    //region get/set

    public String getApiUserName() {
        return apiUserName;
    }

    public void setApiUserName(String apiUserName) {
        this.apiUserName = apiUserName;
    }

    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getContextId() {
        return contextId;
    }

    public void setContextId(String contextId) {
        this.contextId = contextId;
    }

    public List<SyncResult> getSyncResultData() {
        return syncResultData;
    }

    public void setSyncResultData(List<SyncResult> syncResultData) {
        this.syncResultData = syncResultData;
    }

    //endregion

    //region 内部类

    /**
     * 同步的结果实体
     */
    public class SyncResult{
        /**
        * 菠萝派日志id
        */
        private String polyApiRequestId;

        /**
        * 匹配表id
        */
        private int apiSysMatchId;

        /**
        * 是否同步成功
        */
        private Boolean isSuccess;

        /**
        * 同步数量
        */
        private int syncCount;

        /**
        * 同步结果消息
        */
        private String syncMsg;

        /**
        * 获取或设置  外部店铺id
        */
        private String outShopId;

        /** 
        * 获取或设置 平台商品id。 
        */ 
        private String platProductId;

        /** 
        * 获取或设置 平台规格id。 
        */ 
        private String platSkuId;

        /**
        * 是否重置为待同步
        */
        private Boolean isResetWaitSync;

        /**
        * 获取或设置 错误代码
        */
        private String subCode;

        /**
        * 获取或设置 增量标识
        */
        private Boolean increFlag;

        /**
        * 获取或设置 活动结束时间
        */
        private LocalDateTime activityEndTime;

        /**
        * 合作编码
        */
        private String vipCooperationNo;

        /**
        * 获取或设置 门店id(京东到家、有赞等)
        */
        private String platStoreId;

        /**
        * 获取或设置 仓库编码(唯品会JIT)
        */
        private String whseCode;

        /**
         * 回传的请求数据
         */
        private String jsonBackData;

        /**
         * 回传的请求数据
         */
        private SyncStockJsonBackData jsonBack;

        //region get/set
        public String getPolyApiRequestId() {
            return polyApiRequestId;
        }

        public void setPolyApiRequestId(String polyApiRequestId) {
            this.polyApiRequestId = polyApiRequestId;
        }

        public int getApiSysMatchId() {
            return apiSysMatchId;
        }

        public void setApiSysMatchId(int aPISysMatchid) {
            this.apiSysMatchId = aPISysMatchid;
        }

        public Boolean getSuccess() {
            return isSuccess;
        }

        public void setSuccess(Boolean success) {
            isSuccess = success;
        }

        public int getSyncCount() {
            return syncCount;
        }

        public void setSyncCount(int syncCount) {
            this.syncCount = syncCount;
        }

        public String getSyncMsg() {
            return syncMsg;
        }

        public void setSyncMsg(String syncMsg) {
            this.syncMsg = syncMsg;
        }

        public String getJsonBackData() {
            return jsonBackData;
        }

        public void setJsonBackData(String jsonBackData) {
            this.jsonBackData = jsonBackData;
        }

        public String getOutShopId() {
            return outShopId;
        }

        public void setOutShopId(String outShopId) {
            this.outShopId = outShopId;
        }

        public String getPlatProductId() {
            return platProductId;
        }

        public void setPlatProductId(String platProductId) {
            this.platProductId = platProductId;
        }

        public String getPlatSkuId() {
            return platSkuId;
        }

        public void setPlatSkuId(String platSkuId) {
            this.platSkuId = platSkuId;
        }

        public Boolean getResetWaitSync() {
            return isResetWaitSync;
        }

        public void setResetWaitSync(Boolean resetWaitSync) {
            isResetWaitSync = resetWaitSync;
        }

        public String getSubCode() {
            return subCode;
        }

        public void setSubCode(String subCode) {
            this.subCode = subCode;
        }

        public Boolean getIncreFlag() {
            return increFlag;
        }

        public void setIncreFlag(Boolean increFlag) {
            this.increFlag = increFlag;
        }

        public LocalDateTime getActivityEndTime() {
            return activityEndTime;
        }

        public void setActivityEndTime(LocalDateTime activityEndTime) {
            this.activityEndTime = activityEndTime;
        }

        public String getVipCooperationNo() {
            return vipCooperationNo;
        }

        public void setVipCooperationNo(String vipCooperationNo) {
            this.vipCooperationNo = vipCooperationNo;
        }

        public String getPlatStoreId() {
            return platStoreId;
        }

        public void setPlatStoreId(String platStoreId) {
            this.platStoreId = platStoreId;
        }

        public String getWhseCode() {
            return whseCode;
        }

        public void setWhseCode(String whseCode) {
            this.whseCode = whseCode;
        }

        public SyncStockJsonBackData getJsonBack() {
            return jsonBack;
        }

        public void setJsonBack(SyncStockJsonBackData jsonBack) {
            this.jsonBack = jsonBack;
        }
        //endregion
    }

    //endregion
}
