package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 售后单处理插件-前置过滤订单
 *
 * <AUTHOR>
 * @date 2024/7/22 上午10:46
 */
public abstract class AbstractPreFiltrationOrderHandle<O> implements IPreFiltrationOrderHandle<O>, IInitContext {
    //region 属性
    /**
     * 日志
     */
    protected static final Logger log = LoggerFactory.getLogger(AbstractPreFiltrationOrderHandle.class);

    /**
     * 上下文
     */
    protected AfterSaleSaveContext context;

    /**
     * 覆盖方法
     */
    private IPreFiltrationOrderHandle<O> coveringMethod;
    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    protected AbstractPreFiltrationOrderHandle(AfterSaleSaveContext context) {
        init(context);
    }
    //endregion

    //region 实现接口方法

    /**
     * 初始化
     *
     * @param context 上下文
     */
    public void init(AfterSaleSaveContext context) {
        this.context = context;
    }

    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult preFiltration(SourceAfterSaleOrderItem<O> orderItem, TargetCovertOrderItem targetOrder) {
        try {
            // 插件业务/覆写逻辑执行
            return coveringMethod != null
                    ? coveringMethod.preFiltration(orderItem, targetOrder)
                    : preFiltrationOrder(orderItem, targetOrder);
        } catch (Exception e) {
            String massage = String.format("【%s】【%s】【%s】售后单前置过滤订单失败-%s，原因：%s", context.getMemberName(), context.getShopId(), orderItem.getAfterSaleNo(), caption(), e.getMessage());
            log.error(massage, e);
            return GoodsConvertHandleResult.failed(massage);
        }
    }
    //endregion

    //region 供子类重写

    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    protected abstract AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<O> orderItem, TargetCovertOrderItem targetOrder);

    /**
     * 标题
     *
     * @return 标题
     */
    protected abstract String caption();
    //endregion

    //region get/set

    public void setCoveringMethod(IPreFiltrationOrderHandle<O> coveringMethod) {
        this.coveringMethod = coveringMethod;
    }

    //endregion
}
