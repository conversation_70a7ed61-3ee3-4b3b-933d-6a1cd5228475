package com.differ.wdgj.api.user.biz.domain.stock;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.mq.DotNetSyncStockResult;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.SyncStockProcessManager;

import java.util.List;

/**
 * 库存同步-外部调用
 *
 * <AUTHOR>
 * @date 2024-02-23 13:41
 */
public class SyncStockFacade implements ISyncStockFacade {
    /**
     * 库存变动通知暂存 | 系统货品->平台商品
     *
     * @param memberName    会员名
     */
    @Override
    public void platStockNoticeSave(String memberName) {

    }

    /**
     * 自动库存同步触发
     *
     * @param syncStockMode  库存同步模式
     * @param triggerMessage 触发信息
     */
    @Override
    public void autoSyncStockTrigger(SyncStockModeEnum syncStockMode, Object triggerMessage) {

    }

    /**
     * 手动库存同步触发
     *
     * @param vipUser    会员名
     * @param sysMatchIds   商品匹配表Id
     */
    @Override
    public void manualSyncStockTrigger(String vipUser, List<String> sysMatchIds) {

    }

    /**
     * 库存同步结果回写（dotNet兼容逻辑）
     *
     * @param syncStockResult dotNet库存同步结果
     */
    @Override
    public StockContentResult<Object> saveSyncStockResult(DotNetSyncStockResult syncStockResult) {
        SyncStockProcessManager manager = new SyncStockProcessManager();
        return manager.saveSyncStockResult(syncStockResult.getOutAccount(), syncStockResult);
    }
}
