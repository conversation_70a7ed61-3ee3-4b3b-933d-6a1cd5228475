package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 库存同步结果处理抽象基类
 *
 * <AUTHOR>
 * @date 2024-03-04 10:37
 */
public abstract class AbstractStockResultProcessor implements ISyncStockShopResultProcessor {

    protected static final Logger log = LoggerFactory.getLogger(AbstractStockResultProcessor.class);


    // region 接口实现

    /**
     * 库存同步结果后置处理
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    public void postProcess(StockSyncContext context, StockSyncResultPackage resultPackage) {
        try {

            // 仅当存在失败结果时运行
            if (this.justRunWhenFailed() && resultPackage.isAllSuccess()) {
                return;
            }

            // 处理结果
            this.processResults(context, resultPackage);

        } catch (Exception e) {
            String massage = String.format("【%s】库存同步结果处理失败-%s，原因：", context.getVipUser(), caption());
            log.error(massage, e);
        }
    }

    // endregion

    // region 供子类重写

    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    protected boolean justRunWhenFailed() {
        return false;
    }

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    protected abstract void processResults(StockSyncContext context, StockSyncResultPackage resultPackage);

    /**
     * 标题
     *
     * @return 标题
     */
    protected abstract String caption();

    // endregion
}
