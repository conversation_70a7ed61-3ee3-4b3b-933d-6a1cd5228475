package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.order;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractOrderConvertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleLogUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnLogDO;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyExchangeStatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 换货单-订单级基础数据转换插件
 *
 * <AUTHOR>
 * @date 2024/8/8 上午10:10
 */
public class ExchangeOrderCovertHandle extends AbstractOrderConvertHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 构造
    public ExchangeOrderCovertHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult convertOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 基础信息
        if (dbOrder.getAfterSaleOrder() != null) {
            afterSaleOrder.setBillId(dbOrder.getAfterSaleOrder().getBillId());
            afterSaleOrder.setCurStatus(dbOrder.getAfterSaleOrder().getCurStatus());
        }
        afterSaleOrder.setShopID(context.getShopId());
        afterSaleOrder.setGetTime(context.getLoadTime());
        // 原始单信息
        if (dbOrder.getApiTrade() != null) {
            afterSaleOrder.setOldBillID(dbOrder.getApiTrade().getBillId());
        }
        afterSaleOrder.setOldTid(ployOrder.getPlatOrderNo());
        afterSaleOrder.setOldOid(ployOrder.getPlatSubOrderNo());
        // 基础售后信息
        afterSaleOrder.setRefundId(ployOrder.getExchangeOrderNo());
        afterSaleOrder.setType(WdgjRefundTypeEnum.EXCHANGE.getValue());
        afterSaleOrder.setReturnStatus(covertWdgjRefundStatus(ployOrder));
        afterSaleOrder.setCreatedTime(ployOrder.getCreateTime());
        afterSaleOrder.setReturnReason(AfterSaleCovertUtils.replaceEmoji(StringUtils.substring(ployOrder.getReason(), 0, 50)));
        afterSaleOrder.setRefundFee(BigDecimal.ZERO);
        afterSaleOrder.setRemark(AfterSaleCovertUtils.replaceEmoji(ployOrder.getDesc()));
        // 退货物流信息
        afterSaleOrder.setLogisticName(ployOrder.getBuyerLogisticName());
        afterSaleOrder.setLogisticNo(ployOrder.getBuyerLogisticNo());
        // 买家信息
        afterSaleOrder.setCustomerId(ployOrder.getBuyerNick());
        afterSaleOrder.setChgSndTo(ployOrder.getBuyerName());
        afterSaleOrder.setChgTel(ployOrder.getBuyerPhone());
        afterSaleOrder.setChgProvince(ployOrder.getProvince());
        afterSaleOrder.setChgCity(ployOrder.getCity());
        afterSaleOrder.setChgTown(ployOrder.getArea());
        afterSaleOrder.setChangeAdr(StringUtils.defaultIfEmpty(ployOrder.getBuyerAddress(), ployOrder.getAddress()));
        afterSaleOrder.setReceiverMaskId(ployOrder.getOaid());

        // 处理类别
        AfterSaleProcessTypeEnum processType = dbOrder.getAfterSaleOrder() == null
                ? AfterSaleProcessTypeEnum.INSERTER
                : AfterSaleProcessTypeEnum.UPDATE;
        targetOrder.setProcessType(processType);

        // 日志信息
        ApiReturnLogDO apiReturnLog = AfterSaleLogUtils.covertLogNoBillId(context, processType);
        targetOrder.getAfterSaleOrderLogs().add(apiReturnLog);

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "换货单-订单级基础数据转换";
    }
    //endregion

    /**
     * 转换网店管家售后状态
     *
     * @param ployOrder 菠萝派退货退款单
     * @return 网店管家售后状态
     */
    private String covertWdgjRefundStatus(BusinessGetExchangeOrderResponseOrderItem ployOrder) {
        PolyExchangeStatusEnum polyExchangeStatusEnum = PolyExchangeStatusEnum.create(ployOrder.getOrderStatus());
        if (polyExchangeStatusEnum == null) {
            return "未知状态";
        }

        return polyExchangeStatusEnum.getWdgjValue();
    }
}
