package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceExchangeGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 售后单处理插件-换货/补寄商品级数据转换
 *
 * <AUTHOR>
 * @date 2024/8/8 下午4:14
 */
public abstract class AbstractExchangeGoodsConvertHandle<O, E> implements IExchangeGoodsConvertHandle<O, E>, IInitContext {
    //region 属性
    /**
     * 日志
     */
    protected static final Logger log = LoggerFactory.getLogger(AbstractExchangeGoodsConvertHandle.class);

    /**
     * 上下文
     */
    protected AfterSaleSaveContext context;

    /**
     * 覆盖插件
     */
    private IExchangeGoodsConvertHandle<O, E> coveringHandle;

    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    protected AbstractExchangeGoodsConvertHandle(AfterSaleSaveContext context) {
        init(context);
    }
    //endregion

    //region 实现接口方法

    /**
     * 初始化
     *
     * @param context 上下文
     */
    @Override
    public void init(AfterSaleSaveContext context) {
        this.context = context;
    }

    /**
     * 转换商品级信息
     *
     * @param orderItem     原始售后单数据
     * @param goodsItem     原始售后退货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    @Override
    public GoodsConvertHandleResult convert(SourceAfterSaleOrderItem<O> orderItem, SourceExchangeGoodsItem<E> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        try {
            // 插件业务/覆写逻辑执行
            return coveringHandle != null
                    ? coveringHandle.convert(orderItem, goodsItem, targetOrder, exchangeGoods)
                    : convertGoods(orderItem, goodsItem, targetOrder, exchangeGoods);
        } catch (Exception e) {
            String massage = String.format("【%s】【%s】【%s】售后单商品级数据转换处理失败-%s，原因：%s", context.getMemberName(), context.getShopId(), orderItem.getAfterSaleNo(), caption(), e.getMessage());
            log.error(massage, e);
            return GoodsConvertHandleResult.failed(massage);
        }
    }

    //endregion

    //region 供子类重写

    /**
     * 转换商品级信息-供子类重写
     *
     * @param orderItem     原始售后单数据
     * @param goodsItem     原始售后退货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    protected abstract GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<O> orderItem, SourceExchangeGoodsItem<E> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods);

    /**
     * 标题
     *
     * @return 标题
     */
    protected abstract String caption();
    //endregion

    //region get/set
    public void setCoveringHandle(IExchangeGoodsConvertHandle<O, E> coveringHandle) {
        this.coveringHandle = coveringHandle;
    }
    //endregion
}
