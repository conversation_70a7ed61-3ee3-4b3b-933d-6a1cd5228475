package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.OuterApiEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopAuthStatusEnum;

/**
 * Api店铺基础数据
 *
 * <AUTHOR>
 * @date 2024-03-21 20:14
 */
public class ApiShopBaseDto {
    /**
     * 店铺id。
     */
    private int shopId;

    /**
     * 外部平台的店铺ID。
     */
    private String outShopId;

    /**
     * 外部账号名
     */
    private String outAccount;

    /**
     * 外部平台类型
     */
    private OuterApiEnum outPlatTypes;

    /**
     * 店铺名称。
     */
    private String shopName;

    /**
     * 客户店铺唯一标识(有用户的APIKey+(由本站AppKey+外部Token生成)。
     */
    private String token;

    /**
     * 平台
     */
    private PolyPlatEnum plat;

    /**
     * 店铺是否启用。
     */
    private boolean isActived;

    /**
     * 是否删除。
     */
    private boolean isDelete;

    /**
     * 授权状态(对应枚举值ShopAuthStatusEnum)
     */
    private ShopAuthStatusEnum authStatus;

    /**
     * 平台店铺ID
     */
    private String platShopId;

    /**
     * 平台店铺名称
     */
    private String platShopName;

    //region get/set
    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public String getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(String outShopId) {
        this.outShopId = outShopId;
    }

    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }
    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isActived() {
        return isActived;
    }

    public void setActived(boolean actived) {
        isActived = actived;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    public OuterApiEnum getOutPlatTypes() {
        return outPlatTypes;
    }

    public void setOutPlatTypes(OuterApiEnum outPlatTypes) {
        this.outPlatTypes = outPlatTypes;
    }

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    public ShopAuthStatusEnum getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(ShopAuthStatusEnum authStatus) {
        this.authStatus = authStatus;
    }

    public String getPlatShopId() {
        return platShopId;
    }

    public void setPlatShopId(String platShopId) {
        this.platShopId = platShopId;
    }

    public String getPlatShopName() {
        return platShopName;
    }

    public void setPlatShopName(String platShopName) {
        this.platShopName = platShopName;
    }

    //endregion
}
