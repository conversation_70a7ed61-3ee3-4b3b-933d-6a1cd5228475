package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;

/**
 * 库存同步-业务处理-店铺级结果处理接口
 *
 * <AUTHOR>
 * @date 2024-02-26 16:20
 */
public interface ISyncStockShopResultProcessor {

    /**
     * 库存同步结果后置处理
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    void postProcess(StockSyncContext context, StockSyncResultPackage resultPackage);
}
