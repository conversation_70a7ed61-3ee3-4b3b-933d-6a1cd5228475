package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiAllShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiShopConfigDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管家店铺扩展表
 *
 * <AUTHOR>
 * @date 2024/7/31 下午3:46
 */
public interface CfgShopListExtMapper {
    /**
     * 根据店铺Id获取下载订单配置
     *
     * @param shopId 店铺Id
     */
    ApiShopConfigDto getDownloadOrderShopConfig(@Param("shopId") int shopId);

    /**
     * 根据店铺Id获取售后配置
     *
     * @param shopId 店铺Id
     */
    ApiShopConfigDto getAfterSaleShopConfig(@Param("shopId") int shopId);

    /**
     * 根据ShopId获取ApiShopId
     *
     * @param shopId 店铺Id
     */
    Integer getApShopIdByShopId(@Param("shopId") int shopId);

    /**
     * 获取会员级所有店铺
     */
    List<ApiAllShopDto.ShopInfo> getAllShopDto();

    /**
     * 更新下载订单配置
     * @param shopId erp店铺Id
     * @param configValue 店铺配置
     */
    void updateDownloadOrderShopConfig(@Param("shopId")int shopId, @Param("configValue")String configValue);

    /**
     * 更新售后配置
     * @param shopId erp店铺Id
     * @param configValue 店铺配置
     */
    void updateAfterSaleShopConfig(@Param("shopId")int shopId, @Param("configValue")String configValue);

    /**
     * 更新 apiShopId
     * @param shopId erp店铺Id
     * @param apiShopId api店铺Id
     */
    void updateApShopIdByShopId(@Param("shopId")int shopId, @Param("apiShopId") int apiShopId);
}
