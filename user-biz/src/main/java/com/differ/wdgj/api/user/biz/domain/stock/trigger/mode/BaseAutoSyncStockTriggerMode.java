package com.differ.wdgj.api.user.biz.domain.stock.trigger.mode;

import com.differ.wdgj.api.user.biz.domain.stock.data.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.ManualSyncStockTriggerMessage;

/**
 * 库存同步-业务处理模式实现
 * <AUTHOR>
 * @date 2024-02-26 17:36
 */
public class BaseAutoSyncStockTriggerMode implements IAutoSyncStockTriggerMode {

    /**
     * 自动库存同步
     * @param triggerMessage 触发信息
     */
    @Override
    public void autoSyncStockTrigger(AutoSyncStockTriggerMessage triggerMessage) {

    }

    /**
     * 手动库存同步
     * @param triggerMessage 触发信息
     */
    @Override
    public void manualSyncStockTrigger(ManualSyncStockTriggerMessage triggerMessage) {

    }
}
