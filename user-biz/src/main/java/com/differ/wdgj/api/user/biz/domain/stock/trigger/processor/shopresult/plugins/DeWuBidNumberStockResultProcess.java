package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.goods.match.data.ApiSysMatchCooperationNoEnhance;
import com.differ.wdgj.api.user.biz.domain.goods.match.subdomain.impl.BasicsGoodsMatchServiceImpl;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 得物更新出价编号
 *
 * <AUTHOR>
 * @date 2024-03-18 18:32
 */
public class DeWuBidNumberStockResultProcess extends AbstractStockResultProcessor {
    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "得物更新出价编号";
    }


    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        List<ApiSysMatchCooperationNoEnhance> cooperationNos = new ArrayList<>();

        // 遍历匹配
        resultPackage.getComposites().forEach(((guidEnhance, composite) -> {
            // 平台响应
            BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse = composite.getPlatResponse();
            ApiSysMatchDO sysMatch = composite.getMatchEnhance().getSysMatch();
            // 请求成功，且出价编号出现变化
            if (platResponse != null && platResponse.getSuccess() &&
                    StringUtils.isNotEmpty(platResponse.getVipCooperationNo()) && !StringUtils.equals(sysMatch.getCooperationNo(), platResponse.getVipCooperationNo())) {
                ApiSysMatchCooperationNoEnhance cooperationNo = new ApiSysMatchCooperationNoEnhance();
                cooperationNo.setId(guidEnhance.getMatchId());
                cooperationNo.setCooperationNo(platResponse.getVipCooperationNo());
                cooperationNos.add(cooperationNo);
            }
        }));

        if(CollectionUtils.isEmpty(cooperationNos)){
            return;
        }

        // 更新出价编号
        new BasicsGoodsMatchServiceImpl().updateGoodsCooperationNo(context.getVipUser(), cooperationNos);
    }
}
