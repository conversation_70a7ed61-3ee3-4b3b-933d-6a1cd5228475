package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins;

import com.differ.wdgj.api.component.task.single.core.JobExecTimeStrategy;
import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.LoadAfterSaleFacade;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadAfterSaleParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleJobUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.auto.AutoLoadAfterShopLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.WdgjApiConst;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.AutoLoadAfterSaleShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AutoJobTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiWorkTaskMapper;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.AbstractUserSimpleDistributeJob;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 触发达到自动下载售后单时间间隔的普通店铺 定时任务
 *
 * <AUTHOR>
 * @date 2024/9/11 下午6:28
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "NormalAfterSaleTriggerLoadMemberJob",
        cron = "0/3 * * * * ?"
)
public class NormalAfterSaleTriggerLoadMemberJob extends AbstractUserSimpleDistributeJob {
    //region 变量
    /**
     * 标题
     */
    private final String CAPTION = "触发自动下载售后单店铺定时任务";

    /**
     * 日志
     */
    private final Logger log = LogFactory.get(CAPTION);

    /**
     * API业务工作任务仓储
     */
    private ApiWorkTaskMapper bizWorkTaskMapper;
    // endregion

    //region 实现基类方法

    /**
     * 获取待执行会员
     *
     * @return 会员集合
     */
    @Override
    protected List<String> getExecutedUsers() {
        return LoadAfterSaleJobUtils.getAutoMembers(AutoJobTypeEnum.AUTO_LOAD_AFTER_SALE);
    }

    /**
     * 实际的执行时间策略（执行频率）
     *
     * @return 执行时间策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        int minFrequency = NumberUtils.toInt(ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTER_SALE_JOB_MIN_FREQUENCY), 60);
        int range = NumberUtils.toInt(ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTER_SALE_JOB_RANGE), 30);
        execTimeStrategy.setRunFrequency(RandomUtils.nextInt(minFrequency, minFrequency + range));
        return execTimeStrategy;
    }

    /**
     * 按会员执行任务
     *
     * @param memberName 会员名
     */
    @Override
    protected void executeByUser(String memberName) {
        StringBuilder infoLogStr = new StringBuilder();
        try {
            // 获取支持自动下载售后单的店铺
            List<AutoLoadAfterSaleShopDto> autoLoadShopList = AutoLoadAfterShopLocalCache.singleton().getCacheThenSource(memberName);
            infoLogStr.append(String.format("支持自动下载店铺：%s。", JsonUtils.toJson(autoLoadShopList))).append(System.lineSeparator());
            if (CollectionUtils.isNotEmpty(autoLoadShopList)) {
                // 查询历史任务信息
                List<ApiWorkTaskDO> bizWorkTasks = DBSwitchUtil.doDBWithUser(memberName,
                        () -> getBizWorkTaskMapper().queryAutoShopLastFinishTask(WorkEnum.LOAD_AFTER_SALE.getValue(), TriggerTypeEnum.AUTO.getByteValue()));
                infoLogStr.append(String.format("历史任务信息：%s;", JsonUtils.toJson(bizWorkTasks))).append(System.lineSeparator());

                // 获取所有店铺
                for (AutoLoadAfterSaleShopDto shopItem : autoLoadShopList) {
                    try {
                        // 发起排队
                        LoadResult queueResult = createShopTask(memberName, shopItem, bizWorkTasks);
                        // 记录调试日志
                        infoLogStr.append(String.format("【%s】售后下载任务创建%s，信息：%s", shopItem.getShopId(), queueResult.isSuccess() ? "成功" : "失败", queueResult.getMessage())).append(System.lineSeparator());
                    } catch (Exception e) {
                        log.error("【{}-{}】售后下载任务创建异常：", memberName, shopItem.getShopId(), e);
                    }
                }

            }
        } finally {
            LogFactory.info(CAPTION, memberName, () -> infoLogStr);
        }
    }

    /**
     * 线程池
     *
     * @return 线程池
     */
    @Override
    protected TaskEnum taskPool() {
        // 默认同步执行，相当于未设置线程池
        return TaskEnum.API_AFTER_SALE_LOAD_QUEUE;
    }

    /**
     * 日志标题
     *
     * @return 日志标题
     */
    @Override
    protected String logCaption() {
        return "触发自动下载售后单店铺定时任务";
    }
    //endregion

    //region 私有方法

    /**
     * 创建店铺级任务
     *
     * @param memberName   会员名
     * @param shopItem     店铺信息
     * @param bizWorkTasks 历史任务信息
     * @return 创建结果
     */
    private LoadResult createShopTask(String memberName, AutoLoadAfterSaleShopDto shopItem, List<ApiWorkTaskDO> bizWorkTasks) {
        if(shopItem.getPlat() == null){
            return LoadResult.failedResult("不支持的平台");
        }
        // 配置键过滤
        if (ConfigKeyUtils.isActionApiMultipleMatchValue(ConfigKeyEnum.CLOSE_AUTO_LOAD_AFTER_SALE_JOB, new String[]{"@"}, shopItem.getPlat().toString(), memberName)) {
            return LoadResult.failedResult("配置键过滤-关闭售后单下载");
        }
        // 配置键过滤
        if (!ConfigKeyUtils.isActionApiMultipleMatchValue(ConfigKeyEnum.ISACTION_AFTERSALE_ENABLEPLATUSER, new String[]{"@"}, shopItem.getPlat().toString(), memberName)) {
            return LoadResult.failedResult("配置键过滤-未开启java售后业务");
        }
        // 获取上一次执行任务，计算是否达到下载时间间隔
        ApiWorkTaskDO lastShopTask = bizWorkTasks.stream().filter(x -> x.getShopId() == shopItem.getShopId()).findFirst().orElse(null);
        if (lastShopTask != null && Duration.between(lastShopTask.getGmtFinish(), LocalDateTime.now()).getSeconds() < shopItem.getInterval()) {
            return LoadResult.failedResult("未达到下载时间间隔");
        }

        // 触发自动下载（存在排队）
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember(memberName);
        param.setPlat(shopItem.getPlat());
        param.setShopId(shopItem.getShopId());
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator(WdgjApiConst.AUTO_OPERATOR);
        param.setLoadArgs(AfterSaleLoadArgs.buildAutoTimeRangeArgs());
        LoadResult queueResult = new LoadAfterSaleFacade().createQueueTask(param);
        if (queueResult.isFailed()) {
            String message = String.format("【%s-%s】售后下载任务创建失败，原因：%s", memberName, shopItem.getShopId(), queueResult.getMessage());
            return LoadResult.failedResult(message);
        }

        return LoadResult.successResult();
    }

    /**
     * 获取工作任务仓储
     *
     * @return 工作任务仓储
     */
    private ApiWorkTaskMapper getBizWorkTaskMapper() {
        if (this.bizWorkTaskMapper == null) {
            bizWorkTaskMapper = BeanContextUtil.getBean(ApiWorkTaskMapper.class);
        }
        return bizWorkTaskMapper;
    }
    //endregion
}
