package com.differ.wdgj.api.user.biz.domain.stock.utils;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.*;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 库存结果处理工具类
 *
 * <AUTHOR>
 * @date 2024-03-12 21:18
 */
public class StockResultUtils {

    //region 构造
    private StockResultUtils(){}
    //endregion

    //region 常量
    /**
     * 降级标识
     */
    private static final List<Integer> TIER_DOWN_FLAGS = Arrays.asList(SyncStockFlagEnum.Limit.getValue(), SyncStockFlagEnum.Forbid.getValue());
    //endregion

    //region 日志相关

    /**
     * 创建菠萝派请求结果
     * @param context 全局上下文
     * @param responseComposite 结果组合
     * @return 菠萝派请求结果
     */
    public static String createPloySyncCause(StockSyncContext context, StockSyncResultComposite responseComposite){
        if(responseComposite == null){
            return "";
        }

        BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse = responseComposite.getPlatResponse();
        if(platResponse == null){
            return "";
        }

        // 多仓结果
        String multiResult = "";
        if(StringUtils.isNotEmpty(platResponse.getWhseCode())){
            multiResult = String.format("【平台仓库：%s】", platResponse.getWhseCode());
        }
        else if(StringUtils.isNotEmpty(platResponse.getPlatStoreId())){
            multiResult = String.format("【平台门店：%s】", platResponse.getPlatStoreId());
        }

        // 同步结果
        String polySyncCause = Boolean.TRUE.equals(platResponse.getSuccess())
                ? "上次同步成功"
                : platResponse.getMessage();
        // 操作
        String operatorName = context.getOperatorName() == null ? "" : context.getOperatorName();
        String operation = StockSyncTriggerTypeEnum.MANUAL_SYNC.equals(context.getTriggerType())
                ? operatorName + context.getTriggerType().getName()
                : context.getTriggerType().getName();
        // 同步数量
        Integer quantity = platResponse.getQuantity();
        // 库存同步详情
        String detailCount = convertDetailCount(responseComposite.getDetailCount());
        // 菠萝派日志id
        String polyApiRequestId = platResponse.getPolyApiRequestId();

        return String.format("[新API] %s%s(%s%s%s)[%s]", multiResult, polySyncCause, operation, quantity, detailCount, polyApiRequestId);
    }

    /**
     * 转换详细库存量
     * @param detailCount 原始详细库存量
     * @return 详细库存量
     */
    public static String convertDetailCount(String detailCount){
        if(StringUtils.isEmpty(detailCount)){
            return "";
        }

        StringBuilder detailAll = new StringBuilder();
        if(StringUtils.isNotEmpty(detailCount)){
            String[] detailArray = detailCount.split(";");
            for (String stockCounts : detailArray) {
                String[] stockCount = stockCounts.split(":");
                if(stockCount.length > 1){
                    Integer countTypeValue = Integer.parseInt(stockCount[0]);
                    StockDetailCountTypeEnum countType = StockDetailCountTypeEnum.create(countTypeValue);
                    Double count = Double.parseDouble(stockCount[1]);
                    if(countType.equals(StockDetailCountTypeEnum.BatchInventory)){
                        detailAll.append(String.format("，%s：%s",countType.getName(), count));
                    }
                    else{
                        if(count > 0){
                            detailAll.append(String.format("，%s：%s",countType.getName(), count));
                        }
                    }
                }
            }
        }

        return detailAll.toString();
    }

    //endregion

    //region 错误码相关

    /**
     * 获取 需要处理的菠萝派日志编码
     * @param plat 平台
     * @param vipUser 会员名
     * @return 需要处理的菠萝派日志编码
     */
    public static Set<String> getNeedProcessErrorCodes(PolyPlatEnum plat, String vipUser){
        Set<String> errorCodes = new HashSet<>();
        if(plat == null || StringUtils.isEmpty(vipUser)){
            return errorCodes;
        }

        String configKeyValue = ConfigKeyUtils.getWdgjConfigValue(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess);
        String[] configArr = StringUtils.split(configKeyValue, ConfigKeyUtils.REGEX_VERTICAL_LINE);
        for (String config: configArr) {
            String[] subConfigArr = StringUtils.split(config, "[@$]");
            if(subConfigArr.length > 2){
                String configPlat = ConfigKeyUtils.WARNING_SIGNAL + subConfigArr[0] + ConfigKeyUtils.WARNING_SIGNAL;
                String configVipUser = ConfigKeyUtils.WARNING_SIGNAL + subConfigArr[1] + ConfigKeyUtils.WARNING_SIGNAL;
                String nowPlat =  ConfigKeyUtils.WARNING_SIGNAL + plat.getValue().toString() + ConfigKeyUtils.WARNING_SIGNAL;
                String nowVipUser =  ConfigKeyUtils.WARNING_SIGNAL + vipUser + ConfigKeyUtils.WARNING_SIGNAL;
                String nowAll = ConfigKeyUtils.WARNING_SIGNAL + ConfigKeyUtils.ALL + ConfigKeyUtils.WARNING_SIGNAL;
                boolean isTruePlat = nowAll.equalsIgnoreCase(configPlat) || configPlat.contains(nowPlat);
                boolean isTrueVipUser = nowAll.equalsIgnoreCase(configVipUser) || configVipUser.contains(nowVipUser);
                if(isTruePlat && isTrueVipUser){
                    String configValue = subConfigArr[2];
                    String[] errorCodeArray = StringUtils.split(configValue, ConfigKeyUtils.WARNING_SIGNAL);
                    CollectionUtils.addAll(errorCodes, errorCodeArray);
                }
            }
        }

        return errorCodes;
    }

    /**
     * 获取 获取降级时间配置
     * @return 获取降级时间配置
     */
    public static Map<String, Integer> getTierDownGradeTimeConfig(){
        Map<String, Integer> result = new HashMap<>();
        String configKeyValue = ConfigKeyUtils.getWdgjConfigValue(ConfigKeyEnum.IsAction_SyncStock_ErrorCodeLimitTime);
        String[] configValues = configKeyValue.split(ConfigKeyUtils.REGEX_VERTICAL_LINE);
        for (String configValue: configValues){
            String[] strings = configValue.split("\\{\\{");
            if(strings.length > 1){
                String errorCode = strings[0];
                Integer gradeTime = Integer.parseInt(strings[1].substring(0, strings[1].length() - 2));
                result.put(errorCode, gradeTime);
            }
        }

        return result;
    }

    //endregion

    //region 结果存储相关转换
    /**
     * 构建匹配表结果对象
     * @param resultComposite 库存同步结果组合
     * @return 匹配表结果对象
     */
    public static StockSyncApiSysMatchResult convertApiSysMatchResult(StockSyncContext context, StockSyncResultComposite resultComposite){

        ApiSysMatchDO sysMatch = resultComposite.getMatchEnhance().getSysMatch();
        BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse = resultComposite.getPlatResponse();

        StockSyncApiSysMatchResult result = new StockSyncApiSysMatchResult();
        result.setApiSysMatchId(sysMatch.getId());
        // 库存同步结果
        if(Boolean.TRUE.equals(platResponse.getSuccess())){
            String successStr = "SUCCESS";
            String sysLog = platResponse.getMessage();
            if(StringUtils.isEmpty(sysLog) || StringUtils.equalsIgnoreCase(sysLog, successStr)){
                sysLog = "上次同步成功";
            }
            String subSysLog = StringUtils.substring(sysLog, 0, 800);
            result.setSysLog(subSysLog);
            result.setIsSys(SyncStockStatusEnum.SyncSuccess.getValue());
            result.setSysCount(platResponse.getQuantity());
            // 触发标识
            result.setSynFlag(SyncStockSynFlagEnum.None.getValue());
        }
        else{
            result.setIsSys(SyncStockStatusEnum.SyncFail.getValue());
            result.setSysCount(platResponse.getQuantity());
            result.setSysLog(platResponse.getMessage());
        }

        // 同步类型
        String sysGoodsType = result.getSysCount() > 0
                ? SyncStockGoodsTypeEnum.OnSale.getName()
                : SyncStockGoodsTypeEnum.OnWarehouse.getName();
        result.setSysGoodsType(sysGoodsType);

        return result;
    }

    /**
     * 构建库存同步日志实体
     * @param resultComposite 库存同步结果组合
     * @return 库存同步日志实体
     */
    public static ApiPlatSysHisDO convertApiPlatSysHisDO(StockSyncContext context, StockSyncResultComposite resultComposite){
        BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse = resultComposite.getPlatResponse();
        ApiPlatSysHisDO apiPlatSysHisDO = new ApiPlatSysHisDO();
        apiPlatSysHisDO.setId(resultComposite.getMatchEnhance().getSysMatch().getId());
        apiPlatSysHisDO.setNumiid(platResponse.getPlatProductId());
        apiPlatSysHisDO.setSkuId(platResponse.getSkuId());
        apiPlatSysHisDO.setNum(platResponse.getQuantity());
        apiPlatSysHisDO.setbSuccess(Boolean.TRUE.equals(platResponse.getSuccess()) ? 1 : 2);
        String ploySyncCause = StringUtils.substring(createPloySyncCause(context, resultComposite), 0, 800);
        apiPlatSysHisDO.setCause(ploySyncCause);

        return apiPlatSysHisDO;
    }

    /**
     * 是否需要清除标识
     * @param resultComposite 库存同步结果组合
     * @return 结果
     */
    public static boolean isNeedClearFlagMatch(StockSyncResultComposite resultComposite){
        BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse = resultComposite.getPlatResponse();
        ApiSysMatchExtDO extEntity = resultComposite.getMatchEnhance().getSysMatch().getExtEntity();
        // 库存同步成功的情况看下
        if(Boolean.TRUE.equals(platResponse.getSuccess()) && extEntity != null){
            // 1、降级数据，去除降级标识；
            if(TIER_DOWN_FLAGS.contains(extEntity.getIncreFlag())){
                return true;
            }
            // 2、全量库存同步时，去除增量标识
            if(SyncStockTypeEnum.Whole.getCode().equalsIgnoreCase(resultComposite.getPlatRequest().getSyncStockType()) &&
               SyncStockFlagEnum.Increment.getValue().equals(extEntity.getIncreFlag())){
                return true;
            }
        }

        return false;
    }
    //endregion
}
