package com.differ.wdgj.api.user.biz.tasks.job.queue.plugins.aftersale;

import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.LoadAfterSaleFacade;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.ExecLoadAfterSaleParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadResult;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.condition.ConditionalOnSite;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.tasks.job.queue.core.AbstractQueueJob;
import com.differ.wdgj.api.user.biz.tasks.job.queue.core.SimpleQueueJobParameter;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.JobResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.plugins.aftersale.data.AfterSaleLoadQueueJobData;
import com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue.UserClusterJobTaskSourceQueue;

import java.util.Collections;
import java.util.List;

/**
 * 售后单下载排队定时任务
 *
 * <AUTHOR>
 * @date 2024/9/10 下午4:59
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "job.queue.after.sale.load",
        cron = "0/3 * * * * ?"
)
@ConditionalOnSite(sites = {SiteTypeCodeConst.WDGJ_API_BUSINESS})
@SimpleQueueJobParameter(jobCode = "job.queue.after.sale.load", JobQueue = UserClusterJobTaskSourceQueue.class)
public class AfterSaleLoadQueueJob extends AbstractQueueJob<AfterSaleLoadQueueJobData> {
    //region 枚举单例
    /**
     * 枚举单例
     *
     * @return 对象
     */
    public static AfterSaleLoadQueueJob singleton() {
        return AfterSaleLoadQueueJob.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final AfterSaleLoadQueueJob instance;

        private SingletonEnum() {
            instance = new AfterSaleLoadQueueJob();
        }
    }

    //endregion

    //region 重写基类方法
    @Override
    protected List<AfterSaleLoadQueueJobData> getAllAutoInitTasks() {
        return Collections.emptyList();
    }

    @Override
    protected TaskEnum taskPool() {
        return TaskEnum.API_AFTER_SALE_LOAD;
    }

    @Override
    protected JobResult execute(AfterSaleLoadQueueJobData data) {
        try {
            LoadAfterSaleFacade loadAfterSaleFacade = new LoadAfterSaleFacade();
            ExecLoadAfterSaleParam execLoadAfterSaleParam = new ExecLoadAfterSaleParam();
            execLoadAfterSaleParam.setTaskId(data.getTaskId());
            execLoadAfterSaleParam.setMember(data.getMember());
            execLoadAfterSaleParam.setShopId(data.getShopId());
            execLoadAfterSaleParam.setTriggerType(data.getTriggerType());
            execLoadAfterSaleParam.setCreator(data.getOperatorName());
            execLoadAfterSaleParam.setLoadArgs(data.getLoadArgs());
            // 发起下载
            LoadResult loadResult = loadAfterSaleFacade.execTask(execLoadAfterSaleParam);

            // 记录调试日志
            LogFactory.info("售后单下载排队定时任务", data.getMemberName(), () -> ExtUtils.stringBuilderAppend(String.format("【%s-%s】售后单自动下载%s，信息：%s", data.getMemberName(), data.getShopId(), loadResult.isSuccess() ? "成功" : "失败", loadResult.getMessage())));
        } catch (Exception ex) {
            log.error("【{}-{}】下载售后单异常，任务信息：{}；错误信息：", data.getMemberName(), data.getShopId(), JsonUtils.toJson(data), ex);
        }
        return JobResult.FINISH;
    }
    //endregion
}
