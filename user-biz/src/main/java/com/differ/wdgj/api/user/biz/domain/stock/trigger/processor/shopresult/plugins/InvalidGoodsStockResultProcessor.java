package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.goods.match.data.ApiSysMatchOperationEnhance;
import com.differ.wdgj.api.user.biz.domain.goods.match.subdomain.IBasicsGoodsMatchService;
import com.differ.wdgj.api.user.biz.domain.goods.match.subdomain.impl.BasicsGoodsMatchServiceImpl;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存同步结果 - 无效货品
 *
 * <AUTHOR>
 * @date 2024-03-11 9:20
 */
public class InvalidGoodsStockResultProcessor extends AbstractStockResultProcessor {

    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "无效货品";
    }

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        // 无效商品
        List<ApiSysMatchOperationEnhance> invalidGoodsItems = new ArrayList<>();
        List<ApiSysMatchOperationEnhance> invalidSkuItems = new ArrayList<>();

        // 遍历失败结果,找出无效商品
        resultPackage.getFailedItems().forEach(idEnhance -> {

            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (null == composite) {
                return;
            }

            boolean isNeedProcess = resultPackage.getNeedProcessErrorCodes().contains(composite.getResponseErrorCode().name());

            // 平台商品不存在或
            boolean isInvalidGoods = SyncStockPolyErrorCodeEnum.PRODUCT_NOT_EXIST.equals(composite.getResponseErrorCode());
            if (isNeedProcess && isInvalidGoods) {
                invalidGoodsItems.add(ApiSysMatchOperationEnhance.create(composite.getMatchEnhance().getSysMatch(), composite.getPlatResponse().getMessage()));
            }

            // 平台规格不存在
            boolean isInvalidSku = SyncStockPolyErrorCodeEnum.SKU_NOT_EXIST.equals(composite.getResponseErrorCode());
            if (isNeedProcess && isInvalidSku) {
                invalidSkuItems.add(ApiSysMatchOperationEnhance.create(composite.getMatchEnhance().getSysMatch(), composite.getPlatResponse().getMessage()));
            }

        });
        // 基础商品匹配信息操作
        IBasicsGoodsMatchService basicsGoodsMatchService = new BasicsGoodsMatchServiceImpl();
        // 商品维度删除商品匹配
        if(CollectionUtils.isNotEmpty(invalidGoodsItems)){
            basicsGoodsMatchService.delGoodsMatchToNumId(context.getVipUser(), context.getOperatorName(), invalidGoodsItems);
        }
        // 规格维度删除商品匹配
        if (CollectionUtils.isNotEmpty(invalidSkuItems)) {
            basicsGoodsMatchService.delGoodsMatch(context.getVipUser(), context.getOperatorName(), invalidSkuItems);
        }
    }
}
