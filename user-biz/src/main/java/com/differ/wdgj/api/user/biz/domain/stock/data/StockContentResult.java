package com.differ.wdgj.api.user.biz.domain.stock.data;

/**
 * 库存通用结果
 *
 * <AUTHOR>
 * @date 2024-03-04 10:18
 */
public class StockContentResult<T>  {

    // region 属性

    /**
     * 是否成功
     */
    protected boolean success;

    /**
     * 错误编码
     */
    protected String errorCode;

    /**
     * 错误信息
     */
    protected String message;

    /**
     * 内容
     */
    protected T content;


    // endregion

    //region 公用静态方法
    /**
     * 成功结果
     *
     * @param <Content> 泛型
     * @return 结果
     */
    public static <Content> StockContentResult<Content> success() {
        StockContentResult<Content> result = new StockContentResult<>();
        result.setSuccess(true);
        return result;
    }

    /**
     * 成功结果
     *
     * @param content   内容
     * @param <Content> 泛型
     * @return 结果
     */
    public static <Content> StockContentResult<Content> success(Content content) {
        StockContentResult<Content> result = new StockContentResult<>();
        result.setSuccess(true);
        result.setContent(content);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message   错误信息
     * @param <Content> 泛型
     * @return 结果
     */
    public static <Content> StockContentResult<Content> failed(String message) {
        StockContentResult<Content> result = new StockContentResult<>();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }

    //endregion



    // region getter & setter

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getContent() {
        return content;
    }

    public void setContent(T content) {
        this.content = content;
    }

    // endregion

}
