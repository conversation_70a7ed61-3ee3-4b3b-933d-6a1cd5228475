package com.differ.wdgj.api.user.biz.domain.stock.notice;

/**
 * 库存同步-变动通知处理管理器
 *
 * <AUTHOR>
 * @date 2024-02-26 16:44
 */
public class ErpStockNoticeManager {
    /**
     * 库存变动通知暂存 | 平台商品维度
     *
     * @param memberName 会员名
     */
    public void savePlatStockNotice(String memberName){
        // 1、[系统货品维度]库存变动通知 转化为 [平台商品维度]库存变动通知

        // 2、过滤[平台商品维度]库存变动通知

        // 3、保存通知 [平台商品维度]库存变动通知
        // 3.1、多个保存处理者，互斥

        // 4、删除[系统货品维度]库存变动通知
    }
}
