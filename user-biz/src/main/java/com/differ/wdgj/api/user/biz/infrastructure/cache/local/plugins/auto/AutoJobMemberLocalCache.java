package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.auto;

import com.differ.wdgj.api.user.biz.infrastructure.auto.IAutoJobMemberResolver;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AutoJobTypeEnum;
import jodd.util.MathUtil;

import java.util.HashSet;
import java.util.Set;

/**
 * 自动任务会员内存缓存
 *
 * <AUTHOR>
 * @date 2023-11-07 11:11
 */
public class AutoJobMemberLocalCache extends AbstractLocalCache<AutoJobTypeEnum, Set<String>> {
    // region 构造方法
    /**
     * 构造方法
     */
    public AutoJobMemberLocalCache() {
        this.cacheMaxSize = 100;
        this.expire = MathUtil.randomInt(20, 30);
    }
    // endregion

    //region 单例
    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static AutoJobMemberLocalCache singleton() {
        return AutoJobMemberLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final AutoJobMemberLocalCache instance;

        private SingletonEnum() {
            instance = new AutoJobMemberLocalCache();
        }
    }

    //endregion

    // region 重写基类方法
    /**
     * 加载数据
     *
     * @param key 会员
     * @return 结果
     */
    @Override
    protected Set<String> loadSource(AutoJobTypeEnum key) {
        IAutoJobMemberResolver autoJobMemberResolver;
        try {
            autoJobMemberResolver = key.getAutoBizResolverClazz().newInstance();
        } catch (Exception e) {
            LogFactory.error("自动任务会员内存缓存", String.format("【%s】创建自动任务会员解析器失败", key.getName()));
            return new HashSet<>();
        }

        Set<String> autoMembers = autoJobMemberResolver.resolveAutoMembers();
        // 空值兼容处理
        if (autoMembers == null) {
            autoMembers = new HashSet<>();
        }

        return autoMembers;
    }

    // endregion
}
