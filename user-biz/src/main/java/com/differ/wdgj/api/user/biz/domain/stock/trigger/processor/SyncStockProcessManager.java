package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.*;
import com.differ.wdgj.api.user.biz.domain.stock.data.mq.DotNetSyncStockResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.SyncStockTriggerFactory;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.DotNetSyncStockResultOperation;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.StockGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 库存同步-业务处理管理者
 *
 * <AUTHOR>
 * @date 2024-02-26 9:54
 */
public class SyncStockProcessManager {

    private static final Logger log = LoggerFactory.getLogger(SyncStockProcessManager.class);

    /**
     * 触发库存同步
     * @param request
     */
    public void doSyncStock(StockSyncRequest request){
        // 1、基础数据查询


        // 2、构建库存同步请求参数


        // 3、发起菠萝派库存同步请求

        // 4、保存库存同步结果
    }

    /**
     * 保存库存同步结果（dotNet兼容逻辑）
     * @param vipUser 会员名
     * @param syncStockResult dotNet库存同步结果 - 店铺级
     */
    public StockContentResult<Object> saveSyncStockResult(String vipUser, DotNetSyncStockResult syncStockResult) {
        StockContentResult<Object> result = StockContentResult.success();
        try {
            // 数据过滤
            if(StringUtils.isEmpty(vipUser)){
                return StockContentResult.failed("会员名为空");
            }
            if(syncStockResult == null || CollectionUtils.isEmpty(syncStockResult.getSyncResultData())){
                return StockContentResult.failed("dotNet库存同步结果为空");
            }

            // 反序列化jsonBack
            syncStockResult.getSyncResultData().forEach(x -> x.setJsonBack(JsonUtils.deJson(x.getJsonBackData(), SyncStockJsonBackData.class)));

            // 查询匹配数据
            List<Integer> apiSysMatchIds = syncStockResult.getSyncResultData().stream().map(DotNetSyncStockResult.SyncResult::getApiSysMatchId).collect(Collectors.toList());
            List<ApiSysMatchDO> apiSysMatches = StockGoodsMatchOperation.singleton().queryMatch(vipUser, apiSysMatchIds);
            if(CollectionUtils.isEmpty(apiSysMatches)){
                return StockContentResult.failed("匹配数据为空");
            }

            // 构建菠萝派数据
            DotNetSyncStockResultOperation operation = new DotNetSyncStockResultOperation();
            Map<MatchIdEnhance, StockSyncResultComposite> ployResponseComposites = operation.convertToResponseComposite(apiSysMatches, syncStockResult);

            // 构建店铺级上下文
            StockContentResult<StockSyncContext> context = operation.convertToContext(vipUser, syncStockResult);
            if(!context.getSuccess()){
                return StockContentResult.failed(context.getMessage());
            }

            ISyncStockProcessor processor = SyncStockTriggerFactory.createStockSyncProcessor(context.getContent());
            // 保存库存同步结果
            processor.saveSyncStockResult(ployResponseComposites);

        } catch (Exception e) {
            String massage = String.format("【%s】保存库存同步结果，原因：", vipUser);
            log.error(massage, e);
            result.setMessage(massage);
            result.setSuccess(false);
        }
        return result;
    }
}
