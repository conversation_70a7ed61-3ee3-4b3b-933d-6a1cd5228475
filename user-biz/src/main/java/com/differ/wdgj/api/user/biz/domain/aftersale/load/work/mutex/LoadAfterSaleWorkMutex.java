package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.mutex.DefaultWorkMutex;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;

/**
 * 售后下载任务互斥
 *
 * <AUTHOR>
 * @date 2024/9/30 下午4:05
 */
public class LoadAfterSaleWorkMutex extends DefaultWorkMutex {
    /**
     * 创建任务，当已存在互斥的任务未完成时，返回false
     *
     * @param workData    工作数据
     * @param dataOperate 数据操作工具
     * @return 创建结果
     */
    @Override
    public CreateResult createIfNoExists(WorkData<?> workData, WorkDataOperate dataOperate) {
        // 基础数据
        WorkContext workContext = workData.getWorkContext();
        AfterSaleLoadArgs args = (AfterSaleLoadArgs)workData.getData();
        if(args != null){
            // 消息推送不互斥
            AfterSaleLoadTypeEnum loadType = args.getLoadType();
            TriggerTypeEnum triggerType = workContext.getTriggerType();
            if(triggerType == TriggerTypeEnum.MESSAGE_NOTIFICATION && loadType == AfterSaleLoadTypeEnum.AFTER_SALE_NO){
                return dataOperate.create(workData);
            }
        }

        return super.createIfNoExists(workData, dataOperate);
    }
}
