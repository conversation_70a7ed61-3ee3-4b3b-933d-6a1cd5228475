package com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.DownloadOrderShopConfig;

/**
 * 会员店铺配置表 - 业务类型
 *
 * <AUTHOR>
 * @date 2024-06-25 17:21
 */
public enum ApiShopConfigBizTypes implements ValueEnum {
    /**
     * 下载订单
     */
    DOWNLOAD_ORDER(1, "下载订单", DownloadOrderShopConfig.class),

    /**
     * 反写备注
     */
    UPDATE_SELLER_MEMO(2, "反写备注", null),

    /**
     * 委外仓库
     */
    OUTSOURCING_STORAGE(3, "委外仓库", null),

    /**
     * 订单递交
     */
    SUBMIT_ORDER(4, "订单递交", null),

    /**
     * 订单发货
     */
    SEND(5, "订单发货", null),

    /**
     * 同步库存
     */
    SYNC_STOCK(6, "同步库存", null),

    /**
     * 家装服务商（公共配置）
     */
    HOME_PRETEND_SERVICE_PUBLIC(7, "家装服务商", null),

    /**
     * 自动发货物流方式设置（公共配置）
     */
    AUTO_SEND_LOGISTIC_TYPE_SET_PUBLIC(8, "自动发货物流方式设置", null),

    /**
     * 物流关键字匹配（公共配置）
     */
    LOGISTIC_KEYWORD_MATCH(9, "物流关键字匹配", null),

    /**
     * 物流匹配（公共配置）
     */
    LOGISTIC_MATCH_PUBLIC(10, "物流匹配", null),

    /**
     * 商品匹配规则（公共配置）
     */
    GOODS_MATCH_RULE_PUBLIC(11, "商品匹配规则", null),

    /**
     * 发货失败（公共配置）
     */
    SEND_FAIL_PUBLIC(12, "发货失败", null),

    /**
     * 售后相关配置
     */
    AFTER_SALES(13, "售后", AfterSalesShopConfig.class)


    ;

    /**
     * 枚举值<p/>
     * 注意定义需要保持和中心库dev_shopConfig一致
     */
    private final int value;

    /**
     * 枚举描述
     */
    private final String name;

    /**
     * 配置内容类型
     */
    private final Class<?> configValueClazz;

    ApiShopConfigBizTypes(int value, String name, Class<?> configValueClazz) {
        this.value = value;
        this.name = name;
        this.configValueClazz = configValueClazz;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 获取配置内容类型
     * @return 配置内容类型
     */
    public Class<?> getConfigValueClazz() {
        return configValueClazz;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static ApiShopConfigBizTypes create(String value) {
        return EnumConvertCacheUtil.convert(value, ApiShopConfigBizTypes.class, EnumConvertType.VALUE);
    }
}
