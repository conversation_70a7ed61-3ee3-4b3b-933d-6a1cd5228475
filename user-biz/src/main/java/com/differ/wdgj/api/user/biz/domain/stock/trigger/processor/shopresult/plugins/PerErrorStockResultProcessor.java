package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.utils.StockResultUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 库存同步结果 - 错误预处理
 *
 * <AUTHOR>
 * @date 2024-03-05 19:35
 */
public class PerErrorStockResultProcessor extends AbstractStockResultProcessor {

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        // 失败结果
        Set<MatchIdEnhance> failedItems = new HashSet<>();

        // 遍历匹配
        resultPackage.getComposites().forEach(((guidEnhance, composite) -> {
            // 平台响应
            BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse = composite.getPlatResponse();
            if (platResponse == null || platResponse.getSuccess()) {
                return;
            }

            // 转换错误码
            SyncStockPolyErrorCodeEnum errorCode = SyncStockPolyErrorCodeEnum.create(platResponse.getSubCode(), platResponse.getMessage());
            // 未识别赋值未知异常
            if (errorCode == null) {
                errorCode = SyncStockPolyErrorCodeEnum.UNKNOWN_ERROR;
            }
            composite.setResponseErrorCode(errorCode);

            // 添加到失败结果集
            failedItems.add(guidEnhance);
        }));

        // 错误码配置键读取
        if(CollectionUtils.isNotEmpty(failedItems)){
            Set<String> needProcessErrorCodes = StockResultUtils.getNeedProcessErrorCodes(context.getPlat(), context.getVipUser());
            resultPackage.setNeedProcessErrorCodes(needProcessErrorCodes);
        }

        // 封装结果
        resultPackage.setFailedItems(failedItems);
        resultPackage.setAllSuccess(failedItems.isEmpty());
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "错误预处理";
    }

}
