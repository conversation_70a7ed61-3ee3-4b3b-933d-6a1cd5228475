package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchExtResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.StockGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 库存同步结果处理-结果持久化
 *
 * <AUTHOR>
 * @date 2024-03-13 11:24
 */
public class PersistStockResultProcessor extends AbstractStockResultProcessor {

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "结果持久化";
    }

    /**
     * 处理结果
     *
     * @param context       上下文
     * @param resultPackage 库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        Set<StockSyncApiSysMatchResult> apiSysMatchResults = new HashSet<>();
        Set<StockSyncApiSysMatchExtResult> apiSysMatchExtResults = new HashSet<>();
        List<ApiPlatSysHisDO> stockSyncLogs = new ArrayList<>();

        resultPackage.getComposites().forEach(((idEnhance, resultComposite) -> {
            if(resultComposite.getApiSysMatchResult() != null){
                apiSysMatchResults.add(resultComposite.getApiSysMatchResult());
            }
            if(resultComposite.getApiSysMatchExtResult() != null){
                apiSysMatchExtResults.add(resultComposite.getApiSysMatchExtResult());
            }
            if(resultComposite.getStockSyncLog() != null){
                stockSyncLogs.add(resultComposite.getStockSyncLog());
            }
        }));

        // 批量数据库操作
        StockGoodsMatchOperation.singleton().batchSaveSyncStockResult(context.getVipUser(), apiSysMatchResults);
        StockGoodsMatchOperation.singleton().batchSaveSyncStockExtResult(context.getVipUser(), apiSysMatchExtResults);
        StockGoodsMatchOperation.singleton().batchInsertStockSyncLog(context.getVipUser(), stockSyncLogs);
    }
}
