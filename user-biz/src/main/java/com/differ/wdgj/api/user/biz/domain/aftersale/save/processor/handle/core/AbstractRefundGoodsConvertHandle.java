package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 售后单处理插件-退货/退款商品级数据转换
 *
 * <AUTHOR>
 * @date 2024/7/22 上午10:16
 */
public abstract class AbstractRefundGoodsConvertHandle<O, G> implements IRefundGoodsConvertHandle<O, G>, IInitContext {
    //region 属性
    /**
     * 日志
     */
    protected static final Logger log = LoggerFactory.getLogger(AbstractRefundGoodsConvertHandle.class);

    /**
     * 上下文
     */
    protected AfterSaleSaveContext context;

    /**
     * 覆盖插件
     */
    private IRefundGoodsConvertHandle<O, G> coveringHandle;

    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    protected AbstractRefundGoodsConvertHandle(AfterSaleSaveContext context) {
        init(context);
    }
    //endregion

    //region 实现接口方法

    /**
     * 初始化
     *
     * @param context 上下文
     */
    @Override
    public void init(AfterSaleSaveContext context) {
        this.context = context;
    }

    /**
     * 转换商品级信息
     *
     * @param orderItem   原始售后单数据
     * @param goodsItem   原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    @Override
    public GoodsConvertHandleResult convert(SourceAfterSaleOrderItem<O> orderItem, SourceRefundGoodsItem<G> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        try {
            // 插件业务/覆写逻辑执行
            return coveringHandle != null
                    ? coveringHandle.convert(orderItem, goodsItem, targetOrder, refundGoods)
                    : convertGoods(orderItem, goodsItem, targetOrder, refundGoods);
        } catch (Exception e) {
            String massage = String.format("【%s】【%s】【%s】售后单商品级数据转换处理失败-%s，原因：%s", context.getMemberName(), context.getShopId(), orderItem.getAfterSaleNo(), caption(), e.getMessage());
            log.error(massage, e);
            return GoodsConvertHandleResult.failed(massage);
        }
    }

    //endregion

    //region 供子类重写

    /**
     * 转换商品级信息-供子类重写
     *
     * @param orderItem   原始售后单数据
     * @param goodsItem   原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    protected abstract GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<O> orderItem, SourceRefundGoodsItem<G> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods);

    /**
     * 标题
     *
     * @return 标题
     */
    protected abstract String caption();
    //endregion

    //region get/set
    public void setCoveringHandle(IRefundGoodsConvertHandle<O, G> coveringHandle) {
        this.coveringHandle = coveringHandle;
    }
    //endregion
}
