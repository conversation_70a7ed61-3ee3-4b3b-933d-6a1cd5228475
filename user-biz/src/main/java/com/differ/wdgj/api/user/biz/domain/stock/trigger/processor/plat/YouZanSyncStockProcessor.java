package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plat;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.MappingSetTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.BaseSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.ISyncStockShopResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.InvalidStoreStockResultProcessor;

import java.util.ArrayList;
import java.util.List;

/**
 * 有赞库存同步处理类
 *
 * <AUTHOR>
 * @date 2024-03-18 19:25
 */
public class YouZanSyncStockProcessor  extends BaseSyncStockProcessor {
    //region 构造
    /**
     * 构造函数
     *
     * @param context 全局上下文
     */
    public YouZanSyncStockProcessor(StockSyncContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取库存同步结果处理器
     *
     * @return 结果
     */
    @Override
    protected List<ISyncStockShopResultProcessor> getPlatResultProcessors(){
        List<ISyncStockShopResultProcessor> resultProcessors = new ArrayList<>();
        resultProcessors.add(new InvalidStoreStockResultProcessor(MappingSetTypeEnum.YouZanShop));
        return resultProcessors;
    }
}
