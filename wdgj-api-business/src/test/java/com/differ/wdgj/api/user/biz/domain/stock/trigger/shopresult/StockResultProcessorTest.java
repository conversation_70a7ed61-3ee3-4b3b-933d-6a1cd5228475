package com.differ.wdgj.api.user.biz.domain.stock.trigger.shopresult;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.PerErrorStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.TierDownStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;

/**
 * 库存同步结果处理测试
 * <AUTHOR>
 * @date 2024-03-18 14:28
 */
@Ignore
public class StockResultProcessorTest extends AbstractSpringTest {

    /**
     *  库存同步结果 - 错误预处理
     */
    @Test
    public void perErrorStockResultProcessorTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultProcessorTest\\StockSyncContext.json")));
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultProcessorTest\\StockSyncResultPackage.json")));
        StockSyncContext context = JsonUtils.deJson(str, StockSyncContext.class);
        context.setPlat(PolyPlatEnum.BUSINESS_TaoCaiCai);
        StockSyncResultPackage resultPackage = JsonUtils.deJson(str2, StockSyncResultPackage.class);

        PerErrorStockResultProcessor perErrorStockResultProcessor = new PerErrorStockResultProcessor();
        perErrorStockResultProcessor.postProcess(context, resultPackage);
    }


    /**
     * 库存同步结果 - 降级
     */
    @Test
    public void tierDownStockResultProcessorTest() throws IOException{
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultProcessorTest\\StockSyncContext.json")));
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultProcessorTest\\StockSyncResultPackage.json")));
        StockSyncContext context = JsonUtils.deJson(str, StockSyncContext.class);
        context.setPlat(PolyPlatEnum.BUSINESS_TaoCaiCai);
        StockSyncResultPackage resultPackage = JsonUtils.deJson(str2, StockSyncResultPackage.class);
        resultPackage.getComposites().forEach((x, y) ->{
            resultPackage.setFailedItems(Collections.singleton(x));
        });

        TierDownStockResultProcessor tierDownStockResultProcessor = new TierDownStockResultProcessor();
        tierDownStockResultProcessor.postProcess(context, resultPackage);
    }
}
