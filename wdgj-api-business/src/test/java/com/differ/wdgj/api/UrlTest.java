package com.differ.wdgj.api;

import jodd.util.URLDecoder;
import org.junit.Ignore;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/7/15 14:59
 */
@Ignore
public class UrlTest {

    @Test
    public void test(){
        String json = "%7B%22pageSize%22:10,%22pageIndex%22:1,%22plat%22:%22%22,%22module%22:%22%22,%22ruleType%22:%22%22,%22keywordDescription%22:%22授权%22%7D";
        String decode = URLDecoder.decode(json);
        String decode2 = URLDecoder.decode(json,"UTF-8");
    }
}
