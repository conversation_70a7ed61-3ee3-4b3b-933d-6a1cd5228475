package com.differ.wdgj.api.user.biz.domain.stock.trigger.operation;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.*;
import com.differ.wdgj.api.user.biz.domain.stock.data.mq.DotNetSyncStockResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.DotNetSyncStockResultOperation;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * dotNet库存同步结果操作类 测试
 *
 * <AUTHOR>
 * @date 2024-03-15 17:20
 */
@Ignore
public class DotNetSyncStockResultOperationTest
        //extends AbstractSpringTest
{
    /**
     * 操作类
     */
    private DotNetSyncStockResultOperation operation = new DotNetSyncStockResultOperation();

    /**
     * 转换库存同步菠萝派结果集合
     */
    @Test
    public void convertToResponseCompositeTest() throws IOException{
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\DotNetSyncStockResultOperationTest\\DotNetSyncStockResult.json")));;
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\DotNetSyncStockResultOperationTest\\GoodsMatchEnhance.json")));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        ApiSysMatchDO goodsMatch = JsonUtils.deJson(str2, ApiSysMatchDO.class);
        List<ApiSysMatchDO> apiSysMatchList = new ArrayList<>();
        apiSysMatchList.add(goodsMatch);
        result.getSyncResultData().forEach(x -> x.setJsonBack(JsonUtils.deJson(x.getJsonBackData(), SyncStockJsonBackData.class)));
        Map<MatchIdEnhance, StockSyncResultComposite> resultComposites = operation.convertToResponseComposite(apiSysMatchList,result);
        resultComposites.forEach((idEnhance, resultComposite) ->{
            String a = JsonUtils.toJson(resultComposite);
        });
    }

    /**
     * 转换多仓库存同步菠萝派结果集合
     */
    @Test
    public void convertMultiSignToResponseCompositeTest() throws IOException{
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\DotNetSyncStockResultOperationTest\\MultiDotNetSyncStockResult.json")));;
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\DotNetSyncStockResultOperationTest\\MultiGoodsMatchEnhance.json")));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        ApiSysMatchDO goodsMatch = JsonUtils.deJson(str2, ApiSysMatchDO.class);
        List<ApiSysMatchDO> apiSysMatchList = new ArrayList<>();
        apiSysMatchList.add(goodsMatch);
        result.getSyncResultData().forEach(x -> x.setJsonBack(JsonUtils.deJson(x.getJsonBackData(), SyncStockJsonBackData.class)));
        Map<MatchIdEnhance, StockSyncResultComposite> resultComposites = operation.convertToResponseComposite(apiSysMatchList,result);
        resultComposites.forEach((idEnhance, resultComposite) ->{
            String a = JsonUtils.toJson(resultComposite);
        });
    }

    /**
     * 转换库存同步菠萝派结果集合
     */
    @Test
    public void convertToContextTest()  throws IOException{
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\DotNetSyncStockResultOperationTest\\DotNetSyncStockResult.json")));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        result.getSyncResultData().forEach(x -> x.setJsonBack(JsonUtils.deJson(x.getJsonBackData(), SyncStockJsonBackData.class)));
        StockContentResult<StockSyncContext> stockContentResult = operation.convertToContext("api2017", result);
        StockSyncContext content = stockContentResult.getContent();
        String a = JsonUtils.toJson(content);
    }

}
