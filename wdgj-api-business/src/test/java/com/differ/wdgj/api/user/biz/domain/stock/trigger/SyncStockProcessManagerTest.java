package com.differ.wdgj.api.user.biz.domain.stock.trigger;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.mq.DotNetSyncStockResult;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.SyncStockProcessManager;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 库存同步-业务处理管理者测试
 *
 * <AUTHOR>
 * @date 2024-03-18 9:49
 */
@Ignore
public class SyncStockProcessManagerTest extends AbstractSpringTest {

    /**
     * 基础测试数据地址
     */
    private final String basePath = "D:\\esapi-java\\管家java测试数据\\SyncStockProcessManagerTest\\%s";

    private static final Logger log = LoggerFactory.getLogger(SyncStockProcessManagerTest.class);

    /**
     * 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void saveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "DotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 保存多仓库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void saveMultiSignSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "MultiSignDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 无效货品库存同步结果
     */
    @Test
    public void invalidGoodsSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "InvalidGoodsDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 无效货品
     */
    @Test
    public void saveInvalidGoodsSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "InvalidGoodsDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    // region 上下架

    /**
     * 保存库存同步结果（dotNet兼容逻辑）- 上架成功
     */
    @Test
    public void saveSyncStockResultUpGoodsSuccessTest()  throws IOException{
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "UpGoodsSuccessSaveSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 保存库存同步结果（dotNet兼容逻辑）- 上架失败
     */
    @Test
    public void saveSyncStockResultUpGoodsFailedTest()  throws IOException{
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "UpGoodsFailedSaveSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    //endregion

    //region 有赞
    /**
     * 有赞 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void youZanSaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "YouZhanDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 有赞多门店 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void multiStoreYouZanSaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "多门店-YouZhanDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 有赞多门店异常 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void multiStoreErrorYouZanSaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "多门店异常-YouZhanDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }
    //endregion

    //region 得物
    /**
     * 得物 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void deWuSaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "DeWuDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }
    //endregion

    //region 淘宝
    /**
     * 淘宝活动商品 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void taoBaoActivitySaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "TaoBaoActivityDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 淘宝分销商品 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void taoBaoFxSaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "TaoBaoFxDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 淘宝C店数据打标 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void taoBaoCStoreFlagSaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "TaoBaoCStoreFlagDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }

    /**
     * 淘宝C店商品审核变更处理 - 保存库存同步结果（dotNet兼容逻辑）
     */
    @Test
    public void taoBaoCStoreCheckSaveSyncStockResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "TaoBaoCStoreCheckDotNetSyncStockResult.json"))));;
        DotNetSyncStockResult result = JsonUtils.deJson(str, DotNetSyncStockResult.class);
        StockContentResult<Object> stockContentResult = new SyncStockProcessManager().saveSyncStockResult("api2017", result);
        Assert.assertNotNull(stockContentResult);
        Assert.assertTrue(stockContentResult.getSuccess());
    }
    //endregion
}
