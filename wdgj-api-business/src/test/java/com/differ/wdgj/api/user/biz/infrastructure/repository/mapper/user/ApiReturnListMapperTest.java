package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 售后单仓储(g_api_return_list)
 * <AUTHOR>
 * @date 2024-06-27 20:38
 */
@Ignore
public class ApiReturnListMapperTest  extends AbstractSpringTest {
    @Autowired
    ApiReturnListMapper mapper;

    /**
     * 根据售后单号查询售后单
     */
    @Test
    public void selectByRefundIdTest(){
        List<String> refundIds = new ArrayList<>();
        refundIds.add("85048311251626359");
        refundIds.add("85048311251626350");
        refundIds.add("770176672");
        refundIds.add("769734461");
        List<ApiReturnListDO> refundOrders = DBSwitchUtil.doDBWithUser("api2017", () -> mapper.selectByRefundId(1212, refundIds));
    }

    /**
     * 单个新增售后单
     */
    @Test
    public void insertTest(){
        //region 测试对象
        ApiReturnListDO apiReturnListDO = new ApiReturnListDO();
        apiReturnListDO.setBillId(12345);
        apiReturnListDO.setRefundId("R123456");
        apiReturnListDO.setTradeID(56789);
        apiReturnListDO.setGetTime(LocalDateTime.now());
        apiReturnListDO.setCurStatus(0);
        apiReturnListDO.setOldBillID(98765);
        apiReturnListDO.setOldTid("T987654");
        apiReturnListDO.setOldOid("O987654321");
        apiReturnListDO.setCreatedTime(LocalDateTime.now());
        apiReturnListDO.setReturnReason("商品质量问题");
        apiReturnListDO.setReturnStatus("已退款");
        apiReturnListDO.setRemark("退款成功");
        apiReturnListDO.setType(1);
        apiReturnListDO.setBid(54321);
        apiReturnListDO.setIsPrime((byte) 1);
        apiReturnListDO.setCustomerId("customer123");
        apiReturnListDO.setNickUId("seller456");
        apiReturnListDO.setLogisticName("顺丰快递");
        apiReturnListDO.setLogisticNo("SF123456789");
        apiReturnListDO.setShopID(123);
        apiReturnListDO.setJitWareHouse("唯品会仓库");
        apiReturnListDO.setChgSndTo("张三");
        apiReturnListDO.setChgTel("13812345678");
        apiReturnListDO.setChgProvince("浙江省");
        apiReturnListDO.setChgCity("杭州市");
        apiReturnListDO.setChgTown("西湖区");
        apiReturnListDO.setChangeAdr("浙江省杭州市西湖区文三路");
        apiReturnListDO.setRefundFee(new BigDecimal("100.0"));
        apiReturnListDO.setParamCode("12345");
        apiReturnListDO.setReserved1("67890");
        apiReturnListDO.setReceiverMaskId("mask123");
        apiReturnListDO.setEncryptType(1);
        apiReturnListDO.setOrderType("normal");
        apiReturnListDO.setbNewReturnFlag(1);
        apiReturnListDO.setBexInterceptAuto(true);
        apiReturnListDO.setExInterceptInvestor("顺丰");
        apiReturnListDO.setExInterceptEnum("拦截中");
        apiReturnListDO.setWhseCode("WH001");
        //endregion

        DBSwitchUtil.doDBWithUser("api2017", () -> mapper.insert(apiReturnListDO));
    }

    /**
     * 批量新增售后单
     */
    @Test
    public void batchInsertTest(){
        //region 测试对象
        ApiReturnListDO apiReturnListDO = new ApiReturnListDO();
        apiReturnListDO.setBillId(12345);
        apiReturnListDO.setRefundId("R100000");
        apiReturnListDO.setTradeID(56789);
        apiReturnListDO.setGetTime(LocalDateTime.now());
        apiReturnListDO.setCurStatus(0);
        apiReturnListDO.setOldBillID(98765);
        apiReturnListDO.setOldTid("T987654");
        apiReturnListDO.setOldOid("O987654321");
        apiReturnListDO.setCreatedTime(LocalDateTime.now());
        apiReturnListDO.setReturnReason("商品质量问题");
        apiReturnListDO.setReturnStatus("已退款");
        apiReturnListDO.setRemark("退款成功");
        apiReturnListDO.setType(1);
        apiReturnListDO.setBid(54321);
        apiReturnListDO.setIsPrime((byte) 1);
        apiReturnListDO.setCustomerId("customer123");
        apiReturnListDO.setNickUId("seller456");
        apiReturnListDO.setLogisticName("顺丰快递");
        apiReturnListDO.setLogisticNo("SF123456789");
        apiReturnListDO.setShopID(123);
        apiReturnListDO.setJitWareHouse("唯品会仓库");
        apiReturnListDO.setChgSndTo("张三");
        apiReturnListDO.setChgTel("13812345678");
        apiReturnListDO.setChgProvince("浙江省");
        apiReturnListDO.setChgCity("杭州市");
        apiReturnListDO.setChgTown("西湖区");
        apiReturnListDO.setChangeAdr("浙江省杭州市西湖区文三路");
        apiReturnListDO.setRefundFee(new BigDecimal("100.0"));
        apiReturnListDO.setParamCode("12345");
        apiReturnListDO.setReserved1("67890");
        apiReturnListDO.setReceiverMaskId("mask123");
        apiReturnListDO.setEncryptType(1);
        apiReturnListDO.setOrderType("normal");
        apiReturnListDO.setbNewReturnFlag(1);
        apiReturnListDO.setBexInterceptAuto(true);
        apiReturnListDO.setExInterceptInvestor("顺丰");
        apiReturnListDO.setExInterceptEnum("拦截中");
        apiReturnListDO.setWhseCode("WH001");
        //endregion

        List<ApiReturnListDO> list = new ArrayList<>();
        list.add(apiReturnListDO);

        DBSwitchUtil.doDBWithUser("api2017", () -> mapper.batchInsert(list));
    }

    /**
     * 批量新增/修改售后单
     */
    @Test
    public void batchInsertOrUpdateTest(){
        //region 测试对象
        ApiReturnListDO apiReturnListDO = new ApiReturnListDO();
        apiReturnListDO.setRefundId("R100012");
        apiReturnListDO.setTradeID(56789);
        apiReturnListDO.setGetTime(LocalDateTime.now());
        apiReturnListDO.setCurStatus(0);
        apiReturnListDO.setOldBillID(98765);
        apiReturnListDO.setOldTid("T987654");
        apiReturnListDO.setOldOid("O987654321");
        apiReturnListDO.setCreatedTime(LocalDateTime.now());
        apiReturnListDO.setReturnReason("商品质量问题");
        apiReturnListDO.setReturnStatus("已退款");
        apiReturnListDO.setRemark("退款成功");
        apiReturnListDO.setType(1);
        apiReturnListDO.setBid(54321);
        apiReturnListDO.setIsPrime((byte) 1);
        apiReturnListDO.setCustomerId("customer123");
        apiReturnListDO.setNickUId("seller456");
        apiReturnListDO.setLogisticName("顺丰快递");
        apiReturnListDO.setLogisticNo("SF123456789");
        apiReturnListDO.setShopID(123);
        apiReturnListDO.setJitWareHouse("唯品会仓库");
        apiReturnListDO.setChgSndTo("张三");
        apiReturnListDO.setChgTel("13812345678");
        apiReturnListDO.setChgProvince("浙江省");
        apiReturnListDO.setChgCity("杭州市");
        apiReturnListDO.setChgTown("西湖区");
        apiReturnListDO.setChangeAdr("浙江省杭州市西湖区文三路");
        apiReturnListDO.setRefundFee(new BigDecimal("100.0"));
        apiReturnListDO.setParamCode("12345");
        apiReturnListDO.setReserved1("67890");
        apiReturnListDO.setReceiverMaskId("mask123");
        apiReturnListDO.setEncryptType(1);
        apiReturnListDO.setOrderType("normal");
        apiReturnListDO.setbNewReturnFlag(1);
        apiReturnListDO.setBexInterceptAuto(true);
        apiReturnListDO.setExInterceptInvestor("顺丰");
        apiReturnListDO.setExInterceptEnum("拦截中");
        apiReturnListDO.setWhseCode("WH001");
        //endregion

        List<ApiReturnListDO> list = new ArrayList<>();
        list.add(apiReturnListDO);

        DBSwitchUtil.doDBWithUser("api2017", () -> mapper.batchInsertOrUpdate(list));
        int billId = list.stream().findFirst().orElse(null).getBillId();
    }

    /**
     * 单个更新
     */
    @Test
    public void updateTest(){
        //region 测试对象
        ApiReturnListDO apiReturnListDO = new ApiReturnListDO();
        apiReturnListDO.setBillId(616778);
        apiReturnListDO.setRefundId("R100000");
        apiReturnListDO.setTradeID(56789);
        apiReturnListDO.setGetTime(LocalDateTime.now());
        apiReturnListDO.setCurStatus(0);
        apiReturnListDO.setOldBillID(98765);
        apiReturnListDO.setOldTid("T987654");
        apiReturnListDO.setOldOid("O987654321");
        apiReturnListDO.setCreatedTime(LocalDateTime.now());
        apiReturnListDO.setReturnReason("商品质量问题");
        apiReturnListDO.setReturnStatus("已退款");
        apiReturnListDO.setRemark("退款成功");
        apiReturnListDO.setType(1);
        apiReturnListDO.setBid(54321);
        apiReturnListDO.setIsPrime((byte) 1);
        apiReturnListDO.setCustomerId("customer123");
        apiReturnListDO.setNickUId("seller456");
        apiReturnListDO.setLogisticName("顺丰快递");
        apiReturnListDO.setLogisticNo("SF123456789");
        apiReturnListDO.setShopID(123);
        apiReturnListDO.setJitWareHouse("唯品会仓库");
        apiReturnListDO.setChgSndTo("张三");
        apiReturnListDO.setChgTel("13812345678");
        apiReturnListDO.setChgProvince("浙江省");
        apiReturnListDO.setChgCity("杭州市");
        apiReturnListDO.setChgTown("西湖区");
        apiReturnListDO.setChangeAdr("浙江省杭州市西湖区文三路");
        apiReturnListDO.setRefundFee(new BigDecimal("100.0"));
        apiReturnListDO.setParamCode("12345");
        apiReturnListDO.setReserved1("67890");
        apiReturnListDO.setReceiverMaskId("mask123");
        apiReturnListDO.setEncryptType(1);
        apiReturnListDO.setOrderType("normal");
        apiReturnListDO.setbNewReturnFlag(1);
        apiReturnListDO.setBexInterceptAuto(true);
        apiReturnListDO.setExInterceptInvestor("顺丰");
        apiReturnListDO.setExInterceptEnum("拦截中");
        apiReturnListDO.setWhseCode("WH001");
        //endregion

        DBSwitchUtil.doDBWithUser("api2017", () -> mapper.update(apiReturnListDO));
    }

    /**
     * 批量更新
     */
    @Test
    public void batchUpdateTest(){
        //region 测试对象
        ApiReturnListDO apiReturnListDO = new ApiReturnListDO();
        apiReturnListDO.setBillId(616777);
        apiReturnListDO.setRefundId("R100001");
        apiReturnListDO.setTradeID(56789);
        apiReturnListDO.setGetTime(LocalDateTime.now());
        apiReturnListDO.setCurStatus(0);
        apiReturnListDO.setOldBillID(98765);
        apiReturnListDO.setOldTid("T987654");
        apiReturnListDO.setOldOid("O987654321");
        apiReturnListDO.setCreatedTime(LocalDateTime.now());
        apiReturnListDO.setReturnReason("商品质量问题");
        apiReturnListDO.setReturnStatus("已退款");
        apiReturnListDO.setRemark("退款成功");
        apiReturnListDO.setType(1);
        apiReturnListDO.setBid(54321);
        apiReturnListDO.setIsPrime((byte) 1);
        apiReturnListDO.setCustomerId("customer123");
        apiReturnListDO.setNickUId("seller456");
        apiReturnListDO.setLogisticName("顺丰快递");
        apiReturnListDO.setLogisticNo("SF123456789");
        apiReturnListDO.setShopID(123);
        apiReturnListDO.setJitWareHouse("唯品会仓库");
        apiReturnListDO.setChgSndTo("张三");
        apiReturnListDO.setChgTel("13812345678");
        apiReturnListDO.setChgProvince("浙江省");
        apiReturnListDO.setChgCity("杭州市");
        apiReturnListDO.setChgTown("西湖区");
        apiReturnListDO.setChangeAdr("浙江省杭州市西湖区文三路");
        apiReturnListDO.setRefundFee(new BigDecimal("100.0"));
        apiReturnListDO.setParamCode("12345");
        apiReturnListDO.setReserved1("67890");
        apiReturnListDO.setReceiverMaskId("mask123");
        apiReturnListDO.setEncryptType(1);
        apiReturnListDO.setOrderType("normal");
        apiReturnListDO.setbNewReturnFlag(1);
        apiReturnListDO.setBexInterceptAuto(true);
        apiReturnListDO.setExInterceptInvestor("顺丰");
        apiReturnListDO.setExInterceptEnum("拦截中");
        apiReturnListDO.setWhseCode("WH001");
        //endregion

        List<ApiReturnListDO> list = new ArrayList<>();
        list.add(apiReturnListDO);

        DBSwitchUtil.doDBWithUser("api2017", () -> mapper.batchUpdate(list));
    }

}
