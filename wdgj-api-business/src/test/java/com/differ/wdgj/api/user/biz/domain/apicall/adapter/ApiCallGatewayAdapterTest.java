package com.differ.wdgj.api.user.biz.domain.apicall.adapter;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallGateway;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.ApiGatewayTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 适配器 - API调用网关地址
 *
 * <AUTHOR>
 * @date 2024/8/26 下午6:40
 */
@Ignore
public class ApiCallGatewayAdapterTest extends AbstractSpringTest {
    //region 常量
    /**
     * 网关适配器
     */
    private final ApiCallGatewayAdapter gatewayAdapter = new ApiCallGatewayAdapter();
    //endregion

    //region 云平台
    /**
     * 京东云网关获取
     */
    @Test
    public void getJdYunGatewayTest(){
        SystemAppConfig.get().setLocalTest(true);
        ApiCallContext context = getContext(PolyAPITypeEnum.BUSINESS_GETORDER, PolyPlatEnum.BUSINESS_JD, "api2017");
        ApiCallGateway gateway = gatewayAdapter.getGateway(context);
        Assert.assertSame(ApiGatewayTypeEnum.YUN, gateway.getType());
    }

    /**
     * 拼多多云网关获取
     */
    @Test
    public void getPddYunGatewayTest(){
        SystemAppConfig.get().setLocalTest(true);
        ApiCallContext context = getContext(PolyAPITypeEnum.BUSINESS_GETORDER, PolyPlatEnum.BUSINESS_Yangkeduo, "api2017");
        ApiCallGateway gateway = gatewayAdapter.getGateway(context);
        Assert.assertSame(ApiGatewayTypeEnum.YUN, gateway.getType());
    }

    /**
     * 唯品会MP云网关获取
     */
    @Test
    public void getWphMpYunGatewayTest(){
        SystemAppConfig.get().setLocalTest(true);
        ApiCallContext context = getContext(PolyAPITypeEnum.BUSINESS_GETORDER, PolyPlatEnum.BUSINESS_WPHMP, "api2017");
        ApiCallGateway gateway = gatewayAdapter.getGateway(context);
        Assert.assertSame(ApiGatewayTypeEnum.YUN, gateway.getType());
    }
    //endregion

    /**
     * 菠萝派网关获取 - 本地测试环境
     */
    @Test
    public void getPolyGatewayForLocalTest(){
        SystemAppConfig.get().setLocalTest(true);
        ApiCallContext context = getContext(PolyAPITypeEnum.BUSINESS_SYNCSTOCK, PolyPlatEnum.BUSINESS_WPHMP, "api2017");
        ApiCallGateway gateway = gatewayAdapter.getGateway(context);
        Assert.assertSame(ApiGatewayTypeEnum.POLY_API, gateway.getType());
    }

    /**
     * 菠萝派网关获取 - 运维功能
     */
    @Test
    public void getPolyGatewayForDevOpsTest(){
        SystemAppConfig.get().setLocalTest(false);
        ApiCallContext context = getContext(PolyAPITypeEnum.BUSINESS_BATCHCHECKREFUNDSTATUS, PolyPlatEnum.BUSINESS_Taobao, "api2017");
        ApiCallGateway gateway = gatewayAdapter.getGateway(context);
        Assert.assertSame(ApiGatewayTypeEnum.POLY_API, gateway.getType());
    }

    /**
     * 菠萝派网关获取 - 配置键
     */
    @Test
    public void getPolyGatewayForConfigKeyTest(){
        SystemAppConfig.get().setLocalTest(false);
        ApiCallContext context = getContext(PolyAPITypeEnum.STORAGE_SYNCGOODS, PolyPlatEnum.STORAGE_AOXIANG, "api2017");
        ApiCallGateway gateway = gatewayAdapter.getGateway(context);
        Assert.assertSame(ApiGatewayTypeEnum.POLY_API, gateway.getType());
    }

    /**
     * 菠萝派网关获取 - 默认
     */
    @Test
    public void getPolyGatewayFoDefaultTest(){
        SystemAppConfig.get().setLocalTest(false);
        ApiCallContext context = getContext(PolyAPITypeEnum.BUSINESS_SYNCSTOCK, PolyPlatEnum.BUSINESS_XiaoHeiYu, "api2017");
        ApiCallGateway gateway = gatewayAdapter.getGateway(context);
        Assert.assertSame(ApiGatewayTypeEnum.POLY_API, gateway.getType());
    }

    //region 私有方法
    /**
     * 获取上下文
     *
     * @param apiType    接口类型
     * @param plat       平台值
     * @param memberName 会员名
     * @return 上下文
     */
    private ApiCallContext getContext(PolyAPITypeEnum apiType, PolyPlatEnum plat, String memberName){
        ApiCallContext context = new ApiCallContext();
        context.setMemberName(memberName);
        context.setApiType(apiType);
        context.setPlat(plat);
        return context;
    }
    //endregion
}
