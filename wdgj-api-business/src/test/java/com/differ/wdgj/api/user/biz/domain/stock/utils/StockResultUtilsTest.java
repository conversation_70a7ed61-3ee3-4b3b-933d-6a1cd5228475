package com.differ.wdgj.api.user.biz.domain.stock.utils;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Set;

/**
 * 库存结果处理工具类 测试
 * <AUTHOR>
 * @date 2024-03-18 10:41
 */
@Ignore
public class StockResultUtilsTest extends AbstractSpringTest {

    /**
     * 创建菠萝派请求结果
     */
    @Test
    public void createPloySyncCauseTest() throws IOException {
        String str1 = String.format("%013d", 1);

        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultUtilsTest\\StockSyncContext.json")));
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultUtilsTest\\StockSyncResultComposite.json")));
        StockSyncContext context = JsonUtils.deJson(str, StockSyncContext.class);
        StockSyncResultComposite resultComposite = JsonUtils.deJson(str2, StockSyncResultComposite.class);
        String result = StockResultUtils.createPloySyncCause(context, resultComposite);
    }

    /**
     * 构建库存同步日志实体
     */
    @Test
    public void convertApiSysMatchResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultUtilsTest\\StockSyncContext.json")));
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultUtilsTest\\StockSyncResultComposite.json")));
        StockSyncContext context = JsonUtils.deJson(str, StockSyncContext.class);
        StockSyncResultComposite resultComposite = JsonUtils.deJson(str2, StockSyncResultComposite.class);
        StockSyncApiSysMatchResult result = StockResultUtils.convertApiSysMatchResult(context, resultComposite);
    }

    /**
     * 构建库存同步日志实体
     */
    @Test
    public void convertApiPlatSysHisDOTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultUtilsTest\\StockSyncContext.json")));
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultUtilsTest\\StockSyncResultComposite.json")));
        StockSyncContext context = JsonUtils.deJson(str, StockSyncContext.class);
        StockSyncResultComposite resultComposite = JsonUtils.deJson(str2, StockSyncResultComposite.class);
        ApiPlatSysHisDO result = StockResultUtils.convertApiPlatSysHisDO(context, resultComposite);
    }

    /**
     * 是否需要清除标识
     */
    @Test
    public void isNeedClearFlagMatchTest() throws IOException {
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockResultUtilsTest\\StockSyncResultComposite.json")));
        StockSyncResultComposite resultComposite = JsonUtils.deJson(str2, StockSyncResultComposite.class);
        boolean result = StockResultUtils.isNeedClearFlagMatch(resultComposite);
    }

    /**
     * 需要处理的菠萝派日志编码
     */
    @Test
    public void getNeedProcessErrorCodesTest(){
        Set<String> test0704 = StockResultUtils.getNeedProcessErrorCodes(PolyPlatEnum.BUSINESS_JD, "test0704");
    }
}
